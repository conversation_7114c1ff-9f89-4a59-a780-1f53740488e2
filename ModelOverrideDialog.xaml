<Window x:Class="FFUCaptureDeploymentTool.ModelOverrideDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FFUCaptureDeploymentTool"
        mc:Ignorable="d"
        Title="Model Override - Select FFU Image" Height="500" Width="700"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style TargetType="TextBlock" x:Key="HeaderStyle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style TargetType="TextBlock" x:Key="InfoStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Style="{StaticResource HeaderStyle}" 
                   Text="Override Model Detection" 
                   HorizontalAlignment="Center"/>
        
        <!-- Info Text -->
        <TextBlock Grid.Row="1" Style="{StaticResource InfoStyle}" 
                   Text="Select an FFU image file to deploy, ignoring the automatic model detection. This allows you to deploy any available FFU image to the current system."
                   Foreground="#666666"/>
        
        <!-- Location Selection -->
        <GroupBox Grid.Row="2" Header="Select Location" Margin="0,10">
            <StackPanel>
                <RadioButton x:Name="NetworkRadioButton" Content="Network Location" 
                             IsChecked="True" Margin="5" 
                             Checked="LocationRadioButton_Checked"/>
                <RadioButton x:Name="UsbRadioButton" Content="USB/External Drive" 
                             Margin="5" 
                             Checked="LocationRadioButton_Checked"/>
            </StackPanel>
        </GroupBox>
        
        <!-- FFU Files List -->
        <GroupBox Grid.Row="3" Header="Available FFU Images" Margin="0,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Top Controls -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Search Box -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,10,0">
                        <TextBlock Text="Search:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBox x:Name="SearchTextBox" Width="200" Height="25" 
                                TextChanged="SearchTextBox_TextChanged"
                                ToolTip="Search by model name or disk size"/>
                    </StackPanel>
                    
                    <!-- Refresh Button -->
                    <Button Grid.Column="1" Content="Refresh List" 
                            Width="100" Height="30"
                            Click="RefreshButton_Click"/>
                </Grid>
                
                <!-- FFU Files ListBox -->
                <ListBox Grid.Row="2" x:Name="FFUFilesListBox" 
                         Margin="0,10,0,0"
                         SelectionChanged="FFUFilesListBox_SelectionChanged">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Margin="5">
                                <TextBlock Text="{Binding ModelName}" FontWeight="Bold"/>
                                <TextBlock Text="{Binding FilePath}" FontSize="10" Foreground="Gray"/>
                                <TextBlock Text="{Binding FileSize}" FontSize="10" Foreground="Blue"/>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Grid>
        </GroupBox>
        
        <!-- Selected File Info -->
        <GroupBox Grid.Row="4" Header="Selected FFU Image" Margin="0,10">
            <TextBlock x:Name="SelectedFileInfoTextBlock" 
                       Text="No FFU image selected" 
                       Style="{StaticResource InfoStyle}"/>
        </GroupBox>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="Clear Override" Click="ClearOverrideButton_Click" 
                    Background="#FF5722" Width="120"/>
            <Button Content="Cancel" Click="CancelButton_Click" 
                    Background="#757575" Width="80"/>
            <Button Content="Select" Click="SelectButton_Click" 
                    x:Name="SelectButton" IsEnabled="False" Width="80"/>
        </StackPanel>
    </Grid>
</Window>
