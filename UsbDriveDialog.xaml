<Window x:Class="FFUCaptureDeploymentTool.UsbDriveDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="External Drive Selection" Height="476" Width="475"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid Margin="21,14,10,7" RenderTransformOrigin="0.532,0.678">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto" MinHeight="33"/>
            <RowDefinition Height="Auto" MinHeight="29.96"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition/>
            <RowDefinition Height="Auto" MinHeight="169.822"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="Configure External Drive Storage" FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>

        <CheckBox x:Name="UseUsbDriveCheckbox" Grid.Row="1" Content="Use external drive for image storage" Margin="0,5,0,5" Checked="UseUsbDriveCheckbox_CheckedChanged" Unchecked="UseUsbDriveCheckbox_CheckedChanged"/>

        <TextBlock Grid.Row="2" Text="Select Drive:" Margin="0,10,0,5"/>

        <Grid Grid.Row="2" Margin="0,31,0,0" Grid.RowSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <ComboBox x:Name="UsbDriveComboBox" Grid.Column="0" Margin="0,5,10,5" IsEnabled="{Binding ElementName=UseUsbDriveCheckbox, Path=IsChecked}"/>
            <Button x:Name="RefreshButton" Grid.Column="1" Content="Refresh" Padding="10,5" Click="RefreshButton_Click"/>
        </Grid>

        <TextBlock Grid.Row="4" TextWrapping="Wrap" Margin="0,10,0,5" Foreground="#1976D2">
            <Run FontWeight="Bold">Note:</Run> All available non-system drives are listed above. If your drive is not showing up, make sure it's properly connected and click Refresh.
        </TextBlock>

        <Border Grid.Row="5" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,10,0,0" Padding="10" Background="#F5F5F5">
            <TextBlock x:Name="DriveInfoTextBlock" TextWrapping="Wrap" Text="No drive selected" />
        </Border>

        <StackPanel Grid.Row="7" Orientation="Horizontal" HorizontalAlignment="Left" Margin="225,40,0,85" Width="210">
            <Button x:Name="CancelButton" Content="Cancel" Width="100" Padding="10,5" Click="CancelButton_Click" Height="27"/>
            <Button x:Name="SaveButton" Content="Save" Width="100" Padding="10,5" Click="SaveButton_Click" Height="27"/>
        </StackPanel>
    </Grid>
</Window>
