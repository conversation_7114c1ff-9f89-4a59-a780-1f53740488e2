<Window x:Class="FFUCaptureDeploymentTool.StatusNotificationPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Operation Status" Height="200" Width="450"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    
    <Window.Resources>
        <Style TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
        </Style>
        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2,5,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Icon and Title -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
            <Image x:Name="StatusIcon" Width="32" Height="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
            <TextBlock x:Name="StatusTitle" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Status Message -->
        <TextBlock Grid.Row="1" x:Name="StatusMessage" FontSize="14" HorizontalAlignment="Center"
                   Margin="0,0,0,20" TextAlignment="Center"/>

        <!-- Additional Details (if any) -->
        <TextBlock Grid.Row="2" x:Name="StatusDetails" FontSize="12" HorizontalAlignment="Center"
                   Margin="0,0,0,20" TextAlignment="Center" Foreground="#666666"/>

        <!-- Buttons -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="OkButton" Content="OK" Click="OkButton_Click" IsDefault="True"/>
        </StackPanel>
    </Grid>
</Window>
