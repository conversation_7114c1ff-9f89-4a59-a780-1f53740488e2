{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj": {"version": "0.5.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj", "projectName": "FFUCaptureDeploymentTool", "projectPath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Costura.Fody": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.8, )", "autoReferenced": true}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}