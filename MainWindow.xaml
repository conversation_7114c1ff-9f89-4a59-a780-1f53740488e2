﻿<Window x:Class="FFUCaptureDeploymentTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FFUCaptureDeploymentTool" d:DataContext="{d:DesignInstance Type=local:FFUViewModel}"
        mc:Ignorable="d"
        Title="Windows Disk (FFU) Capture/Deployment Tool - v0.5b"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen" Icon="/server.png" ResizeMode="NoResize">
    <Window.InputBindings>
        <KeyBinding Key="C" Modifiers="Alt" Command="{Binding CaptureImageCommand}"/>
        <KeyBinding Key="D" Modifiers="Alt" Command="{Binding DeployImageCommand}"/>
        <KeyBinding Key="N" Modifiers="Alt" Command="{Binding CheckNetworkCommand}"/>
        <KeyBinding Key="M" Modifiers="Alt" Command="{Binding ModelOverrideCommand}"/>
        <KeyBinding Key="E" Modifiers="Alt" Command="{Binding SelectUsbDriveCommand}"/>
        <KeyBinding Key="S" Modifiers="Alt" Command="{Binding SelectDiskCommand}"/>
        <KeyBinding Key="X" Modifiers="Alt" Command="{Binding ExitCommand}"/>
    </Window.InputBindings>
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="150"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Width" Value="175"/>
        </Style>

        <Style x:Key="StatusTextConnected" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#4CAF50"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <Style x:Key="StatusTextDisconnected" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#F44336"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <Style x:Key="DeploymentCountStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#FF9800"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <!-- Square Button Style for Settings -->
        <Style x:Key="SettingsSquareButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="80"/>
            <Setter Property="Height" Value="80"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="2*"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <TextBlock Grid.Row="0"
                                           Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Tag}"
                                           FontSize="24"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Margin="0,10,0,0"/>
                                <TextBlock Grid.Row="1"
                                           Text="{TemplateBinding Content}"
                                           TextWrapping="Wrap"
                                           TextAlignment="Center"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Margin="5,0,5,5"
                                           FontSize="11"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Status Panel -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="5" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Row 0 -->
                <TextBlock Grid.Row="0" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Model Detected:"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource InfoTextStyle}" Text="{Binding PcModel}"/>

                <!-- Row 1 -->
                <TextBlock Grid.Row="1" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Model in config file:"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Style="{StaticResource InfoTextStyle}" Text="{Binding CfgModel}"/>

                <!-- Row 2 -->
                <TextBlock Grid.Row="2" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="OS based on config file:"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Style="{StaticResource InfoTextStyle}" Text="{Binding OsType}"/>

                <!-- Row 2.5 - Model Override Status -->
                <TextBlock Grid.Row="2" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Model Override:" Margin="5,25,5,2" Visibility="{Binding IsModelOverrideActive, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,25,5,2" Visibility="{Binding IsModelOverrideActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Style="{StaticResource InfoTextStyle}" Text="ACTIVE - " Foreground="Orange" FontWeight="Bold"/>
                    <TextBlock Style="{StaticResource InfoTextStyle}" Text="{Binding OverrideModelName}"/>
                </StackPanel>

                <!-- Row 3 -->
                <TextBlock Grid.Row="3" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Network Status:"/>
                <TextBlock Grid.Row="3" Grid.Column="1">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource InfoTextStyle}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsNetworkAvailable}" Value="True">
                                    <Setter Property="Text" Value="ONLINE"/>
                                    <Setter Property="Foreground" Value="#4CAF50"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsNetworkAvailable}" Value="False">
                                    <Setter Property="Text" Value="OFFLINE (Click the Check Network button to connect)"/>
                                    <Setter Property="Foreground" Value="#F44336"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- Row 3.5 - USB Drive Status -->
                <TextBlock Grid.Row="3" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="External Drive:" Margin="5,30,5,2"/>
                <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="0,30,5,0">
                    <TextBlock>
                        <TextBlock.Style>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource InfoTextStyle}">
                                <Style.Triggers>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding UseUsbDrive}" Value="True"/>
                                            <Condition Binding="{Binding IsUsbDriveAvailable}" Value="True"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Text" Value="ENABLED"/>
                                        <Setter Property="Foreground" Value="#4CAF50"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </MultiDataTrigger>
                                    <MultiDataTrigger>
                                        <MultiDataTrigger.Conditions>
                                            <Condition Binding="{Binding UseUsbDrive}" Value="True"/>
                                            <Condition Binding="{Binding IsUsbDriveAvailable}" Value="False"/>
                                        </MultiDataTrigger.Conditions>
                                        <Setter Property="Text" Value="NOT AVAILABLE"/>
                                        <Setter Property="Foreground" Value="#F44336"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </MultiDataTrigger>
                                    <DataTrigger Binding="{Binding UseUsbDrive}" Value="False">
                                        <Setter Property="Text" Value="DISABLED"/>
                                        <Setter Property="Foreground" Value="#9E9E9E"/>
                                        <Setter Property="FontWeight" Value="Normal"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <TextBlock Text=" - " Style="{StaticResource InfoTextStyle}" Visibility="{Binding UseUsbDrive, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBlock Text="{Binding SelectedUsbDrive}" Style="{StaticResource InfoTextStyle}" Visibility="{Binding UseUsbDrive, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>

                <!-- Row 3.75 - Selected Disk -->
                <TextBlock Grid.Row="3" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Selected Disk:" Margin="5,60,5,2"/>
                <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="0,60,5,2">
                    <TextBlock Style="{StaticResource InfoTextStyle}" Text="{Binding SelectedDiskNumber, StringFormat='PhysicalDrive{0}'}"/>
                    <TextBlock Style="{StaticResource InfoTextStyle}" Text=" - " Margin="5,0,0,0"/>
                    <TextBlock Style="{StaticResource InfoTextStyle}" Text="{Binding SelectedDiskSize}"/>
                </StackPanel>



                <!-- Row 4 -->
                <TextBlock Grid.Row="4" Grid.Column="0" Style="{StaticResource InfoLabelStyle}" Text="Image Deployment Count:"/>
                <TextBlock Grid.Row="4" Grid.Column="1" Style="{StaticResource DeploymentCountStyle}" Text="{Binding TotalDeploymentCount}" FontSize="14" Margin="5,2,5,2" Height="18.62"/>
            </Grid>
        </Border>

        <!-- Log Display Area -->
        <Border Grid.Row="1" Margin="0,15" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="5">
            <ScrollViewer x:Name="LogScrollViewer" Margin="5" VerticalScrollBarVisibility="Auto">
                <TextBox x:Name="LogTextBox"
                         IsReadOnly="True"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         BorderThickness="0"
                         Background="Transparent"
                         FontFamily="Consolas"
                         Text="{Binding LogContent, Mode=OneWay}"/>
            </ScrollViewer>
        </Border>

        <!-- Action Buttons -->
        <Grid Grid.Row="2" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Content="(C)apture Image" Command="{Binding CaptureImageCommand}"/>
            <Button Grid.Column="1" Content="(D)eploy Image" Command="{Binding DeployImageCommand}"/>
            <Button Grid.Column="2" Content="Check (N)etwork" Command="{Binding CheckNetworkCommand}"/>
        </Grid>

        <!-- Bottom Buttons -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Settings Buttons Panel -->
            <WrapPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="Network Credentials"
                        Tag="🔑"
                        Command="{Binding CredentialsCommand}" Height="92"/>

                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="(E)xternal Drive"
                        Tag="💾"
                        Command="{Binding SelectUsbDriveCommand}" Height="92"/>

                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="(S)elect Disk"
                        Tag="💿"
                        Command="{Binding SelectDiskCommand}" Height="92"/>

                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="Deployment History"
                        Tag="📋"
                        Command="{Binding ViewHistoryCommand}" Height="92"/>

                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="Ignore (M)odel"
                        Tag="🔄"
                        Command="{Binding ModelOverrideCommand}" Height="92"/>

                <Button Style="{StaticResource SettingsSquareButtonStyle}"
                        Content="Revoke Network"
                        Tag="🔒"
                        Background="#FF5B1C18"
                        Click="RevokeNetworkAccess_Click" Height="92"/>
            </WrapPanel>

            <!-- Exit Button -->
            <Button Grid.Column="1" Content="E(x)it" Background="#F44336" Command="{Binding ExitCommand}"
                    Width="150" VerticalAlignment="Center" Margin="10,5,5,5" Height="92"/>
        </Grid>
    </Grid>
</Window>