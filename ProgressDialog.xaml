<Window x:Class="FFUCaptureDeploymentTool.ProgressDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Operation Progress" Height="424" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style TargetType="TextBlock" x:Key="HeaderStyle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style TargetType="TextBlock" x:Key="InfoStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style TargetType="TextBlock" x:Key="ValueStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Icon -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
            <Image x:Name="OperationIcon" Width="24" Height="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBlock x:Name="OperationTitle" Style="{StaticResource HeaderStyle}" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Current Operation Status -->
        <TextBlock Grid.Row="1" x:Name="CurrentOperationText" Style="{StaticResource InfoStyle}" 
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>

        <!-- Progress Bar -->
        <GroupBox Grid.Row="2" Header="Progress" Margin="0,5">
            <StackPanel>
                <ProgressBar x:Name="MainProgressBar" Height="25" Margin="5"
                           Minimum="0" Maximum="100" Value="0"/>
                <TextBlock x:Name="ProgressPercentageText" Text="0%" Style="{StaticResource ValueStyle}"
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </GroupBox>

        <!-- Time Information -->
        <GroupBox Grid.Row="3" Header="Time" Margin="0,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Time Remaining -->
                <TextBlock Grid.Column="0" Text="Time Remaining:" Style="{StaticResource InfoStyle}"/>
                <TextBlock Grid.Column="1" x:Name="TimeRemainingText" Text="Calculating..."
                          Style="{StaticResource ValueStyle}" HorizontalAlignment="Right"/>
            </Grid>
        </GroupBox>

        <!-- Status Messages -->
        <GroupBox Grid.Row="4" Header="Status" Margin="0,5">
            <TextBlock x:Name="StatusMessageText" Text="Initializing..." Style="{StaticResource InfoStyle}"
                      TextAlignment="Center"/>
        </GroupBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
            <Button x:Name="CancelButton" Content="Cancel Operation" Click="CancelButton_Click" 
                    Background="#FF5722" Width="140" Height="44"/>
            <Button x:Name="CloseButton" Content="Close" Click="CloseButton_Click" 
                    Visibility="Collapsed" Width="100"/>
        </StackPanel>
    </Grid>
</Window>
