using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for UsbDriveDialog.xaml
    /// </summary>
    public partial class UsbDriveDialog : Window
    {
        public bool UseUsbDrive { get; private set; }
        public string SelectedDrive { get; private set; } = string.Empty;
        private ObservableCollection<string> _availableUsbDrives;
        private FFUViewModel _viewModel;

        public UsbDriveDialog(ObservableCollection<string> availableUsbDrives, string currentSelectedDrive)
        {
            InitializeComponent();

            _availableUsbDrives = availableUsbDrives;
            _viewModel = (FFUViewModel)Application.Current.MainWindow.DataContext;

            // Populate the combo box with available drives
            UsbDriveComboBox.ItemsSource = _availableUsbDrives;

            // Set the current selection if available
            if (!string.IsNullOrEmpty(currentSelectedDrive) && _availableUsbDrives.Contains(currentSelectedDrive))
            {
                UsbDriveComboBox.SelectedItem = currentSelectedDrive;
                UpdateDriveInfo(currentSelectedDrive);
            }
            else if (_availableUsbDrives.Count > 0)
            {
                UsbDriveComboBox.SelectedIndex = 0;
                if (UsbDriveComboBox.SelectedItem != null)
                {
                    UpdateDriveInfo(UsbDriveComboBox.SelectedItem.ToString() ?? string.Empty);
                }
            }
            else
            {
                DriveInfoTextBlock.Text = "No drives detected. Connect a drive and click Refresh.";
            }

            // Set the checkbox state based on the current configuration
            UseUsbDriveCheckbox.IsChecked = !string.IsNullOrEmpty(currentSelectedDrive);

            // Update UI state
            UsbDriveComboBox.IsEnabled = UseUsbDriveCheckbox.IsChecked == true;

            // Add selection changed event handler
            UsbDriveComboBox.SelectionChanged += UsbDriveComboBox_SelectionChanged;
        }

        private void UsbDriveComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (UsbDriveComboBox.SelectedItem != null)
            {
                string selectedDrive = UsbDriveComboBox.SelectedItem.ToString() ?? string.Empty;
                UpdateDriveInfo(selectedDrive);
            }
        }

        private void UpdateDriveInfo(string driveName)
        {
            if (string.IsNullOrEmpty(driveName))
            {
                DriveInfoTextBlock.Text = "No drive selected";
                return;
            }

            try
            {
                // Extract drive letter from the selected drive (format is "C: (Label) - Size - Format")
                string driveLetter = driveName.Substring(0, 2);
                DriveInfo drive = new DriveInfo(driveLetter);

                if (drive.IsReady)
                {
                    string freeSpace = FormatBytes(drive.AvailableFreeSpace);
                    string totalSpace = FormatBytes(drive.TotalSize);
                    string format = drive.DriveFormat;
                    string type = drive.DriveType.ToString();

                    DriveInfoTextBlock.Text = $"Drive: {driveLetter}\n" +
                                             $"Type: {type}\n" +
                                             $"Format: {format}\n" +
                                             $"Free Space: {freeSpace} of {totalSpace} available\n\n" +
                                             $"Path for FFU images: {driveLetter}\\FFU_images\\";
                }
                else
                {
                    DriveInfoTextBlock.Text = $"Drive {driveLetter} is not ready.";
                }
            }
            catch (System.Exception ex)
            {
                DriveInfoTextBlock.Text = $"Error getting drive information: {ex.Message}";
            }
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }
            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private void UseUsbDriveCheckbox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            UsbDriveComboBox.IsEnabled = UseUsbDriveCheckbox.IsChecked == true;
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // Show a temporary message
            DriveInfoTextBlock.Text = "Refreshing available drives...";

            // Call the RefreshUsbDrives method in the view model
            _viewModel.RefreshUsbDrives();

            // Update the combo box
            UsbDriveComboBox.ItemsSource = _viewModel.AvailableUsbDrives;

            if (_viewModel.AvailableUsbDrives.Count > 0)
            {
                UsbDriveComboBox.SelectedIndex = 0;
                if (UsbDriveComboBox.SelectedItem != null)
                {
                    UpdateDriveInfo(UsbDriveComboBox.SelectedItem.ToString() ?? string.Empty);
                }
            }
            else
            {
                DriveInfoTextBlock.Text = "No drives detected. Connect a drive and click Refresh.";
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            UseUsbDrive = UseUsbDriveCheckbox.IsChecked == true;
            SelectedDrive = UseUsbDrive && UsbDriveComboBox.SelectedItem != null ?
                UsbDriveComboBox.SelectedItem.ToString() ?? string.Empty : string.Empty;

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
