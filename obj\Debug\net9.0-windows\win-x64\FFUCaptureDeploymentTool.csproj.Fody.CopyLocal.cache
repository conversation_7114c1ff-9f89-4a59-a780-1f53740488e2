C:\Users\<USER>\.nuget\packages\system.management\8.0.0\runtimes\win\lib\net8.0\System.Management.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.VisualBasic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.AppContext.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Buffers.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Collections.Concurrent.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Collections.NonGeneric.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Collections.Specialized.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Collections.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ComponentModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Console.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Data.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Data.DataSetExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Data.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.Contracts.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.Debug.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.Process.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.Tools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.Tracing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Drawing.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Drawing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Dynamic.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Formats.Asn1.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Formats.Tar.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Globalization.Calendars.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Globalization.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Globalization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Compression.Brotli.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Compression.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.IsolatedStorage.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Pipes.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Linq.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Linq.Queryable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Http.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.HttpListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Mail.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.NameResolution.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.NetworkInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Ping.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Quic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Requests.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.ServicePoint.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.Sockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.WebClient.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.WebProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.WebSockets.Client.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.WebSockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Net.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Numerics.Vectors.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Private.CoreLib.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Private.DataContractSerialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Private.Uri.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Private.Xml.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Private.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Reflection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Resources.Reader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Resources.ResourceManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Resources.Writer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Handles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.InteropServices.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Intrinsics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Loader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Claims.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Principal.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.SecureString.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ServiceModel.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ServiceProcess.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Text.RegularExpressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Channels.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Overlapped.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Tasks.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Thread.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.ThreadPool.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.Timer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Transactions.Local.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Transactions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.ValueTuple.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Web.HttpUtility.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.ReaderWriter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.XPath.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.XmlDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.XmlSerializer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\mscorlib.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\netstandard.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Accessibility.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\DirectWriteForwarder.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.Win32.Registry.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationCore.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework-SystemCore.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework-SystemData.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework-SystemDrawing.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework-SystemXml.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework-SystemXmlLinq.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Aero.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Aero2.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.AeroLite.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Classic.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Fluent.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Luna.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.Royale.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationFramework.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\PresentationUI.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ReachFramework.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.CodeDom.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.DirectoryServices.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Formats.Nrbf.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.IO.Packaging.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Printing.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Resources.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Cryptography.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Security.Permissions.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Threading.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Windows.Controls.Ribbon.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Windows.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Windows.Input.Manipulations.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Windows.Presentation.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\System.Xaml.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\UIAutomationClient.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\UIAutomationClientSideProviders.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\UIAutomationProvider.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\UIAutomationTypes.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\WindowsBase.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\System.IO.Compression.Native.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\clretwrc.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\clrgc.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\clrgcexp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\clrjit.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\coreclr.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\createdump.exe
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\hostfxr.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\hostpolicy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\mscordaccore.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\mscordaccore_amd64_amd64_9.0.825.36511.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\mscordbi.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\mscorrc.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\msquic.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\D3DCompiler_47_cor3.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\PenImc_cor3.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\PresentationNative_cor3.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\vcruntime140_cor3.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\native\wpfgfx_cor3.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\cs\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\de\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\es\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\fr\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\it\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ja\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ko\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pl\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\pt-BR\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\ru\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\tr\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hans\WindowsBase.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\PresentationCore.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\PresentationFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\PresentationUI.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\ReachFramework.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\System.Xaml.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\UIAutomationClient.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\UIAutomationProvider.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\UIAutomationTypes.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\9.0.8\runtimes\win-x64\lib\net9.0\zh-Hant\WindowsBase.resources.dll
