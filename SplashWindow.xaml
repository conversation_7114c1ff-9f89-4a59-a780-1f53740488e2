<Window x:Class="FFUCaptureDeploymentTool.SplashWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Loading..." Height="240" Width="460"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize" WindowStyle="None" ShowInTaskbar="False" Topmost="True" Icon="/server.png" Background="#FFFFFF">
    <Border CornerRadius="10" BorderThickness="1" BorderBrush="#E0E0E0" Background="#FFFFFF" Padding="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Orientation="Horizontal" Grid.Row="0" Margin="0,0,0,10">
                <Image Source="/server.png" Width="32" Height="32" Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="Windows Disk (FFU) Capture/Deployment Tool" FontWeight="Bold" FontSize="16"/>
                    <TextBlock Text="Starting up..." FontSize="12" Foreground="#666" x:Name="SubtitleText"/>
                </StackPanel>
            </StackPanel>

            <!-- Progress -->
            <StackPanel Grid.Row="1" VerticalAlignment="Center">
                <ProgressBar x:Name="ProgressBar" Height="14" Minimum="0" Maximum="100" Value="0"/>
                <StackPanel Orientation="Horizontal" Margin="0,6,0,0" HorizontalAlignment="Right">
                    <TextBlock x:Name="PercentText" Text="0%" FontWeight="SemiBold"/>
                </StackPanel>
                <TextBlock x:Name="StatusText" Text="Loading..." Margin="0,8,0,0" Foreground="#444"/>
            </StackPanel>

            <!-- Footer -->
            <DockPanel Grid.Row="2" Margin="0,16,0,0">
                <TextBlock Text="Please wait" Foreground="#888"/>
            </DockPanel>
        </Grid>
    </Border>
</Window>

