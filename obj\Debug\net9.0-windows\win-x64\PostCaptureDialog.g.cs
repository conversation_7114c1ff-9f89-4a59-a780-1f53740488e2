﻿#pragma checksum "..\..\..\..\PostCaptureDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "29A97141F82676F7230D77A1CB80589BC53B1973"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FFUCaptureDeploymentTool {
    
    
    /// <summary>
    /// PostCaptureDialog
    /// </summary>
    public partial class PostCaptureDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 58 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DetectedModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OSTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PlatformNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RecoveryPackTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateINIButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\PostCaptureDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SkipButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FFUCaptureDeploymentTool;V0.5.0.0;component/postcapturedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\PostCaptureDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DetectedModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.OSTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.PlatformNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.RecoveryPackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CreateINIButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\PostCaptureDialog.xaml"
            this.CreateINIButton.Click += new System.Windows.RoutedEventHandler(this.CreateINIButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SkipButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\PostCaptureDialog.xaml"
            this.SkipButton.Click += new System.Windows.RoutedEventHandler(this.SkipButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

