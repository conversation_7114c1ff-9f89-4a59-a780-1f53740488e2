﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>False</UseWindowsForms>
    <ApplicationIcon>server.ico</ApplicationIcon>
    <Company>Flex</Company>
    <Authors><PERSON></Authors>
    <Copyright>Copyright © Tomas Utaras 2025</Copyright>
    <Product>Windows Disk (FFU) Capture/Deployment Tool</Product>
    <Description>A tool for capturing and deploying disk images using Microsoft's FFU format</Description>
    <Version>0.5.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- .NET 9.0 Size Optimization Settings -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <DebugType>embedded</DebugType>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    
    <!-- .NET 9.0 Trimming and Optimization -->
    <EnableTrimming>true</EnableTrimming>
    <TrimMode>link</TrimMode>
    <TrimmerRemoveSymbols>true</TrimmerRemoveSymbols>
    <EnableSingleFileAnalyzer>true</EnableSingleFileAnalyzer>
    
    <!-- Native AOT (Optional - for maximum size reduction) -->
    <!-- <PublishAot>true</PublishAot> -->
    <!-- <IlcOptimizationPreference>Size</IlcOptimizationPreference> -->
    
    <!-- Exclude config file from single file packaging -->
    <IncludeAllContentForSelfExtract>false</IncludeAllContentForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="server.png" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="server.ico" />
  </ItemGroup>

  <ItemGroup>
    <None Include="config.cfg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </None>
    <Resource Include="svg\check.svg" />
    <Resource Include="svg\warning.svg" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Costura.Fody" Version="6.0.0" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="server.png" />
  </ItemGroup>
</Project>
