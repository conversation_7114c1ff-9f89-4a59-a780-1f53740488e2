<Window x:Class="FFUCaptureDeploymentTool.PostCaptureDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Capture Complete - Create INI File" Height="500" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Window.Resources>
        <Style TargetType="Label">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,2"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5,2,5,5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Height" Value="25"/>
        </Style>
        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="5,2,5,5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Height" Value="25"/>
        </Style>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Success Icon -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
            <Image Source="pack://application:,,,/svg/check.svg" Width="24" Height="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBlock Text="FFU Capture Completed Successfully!"
                       FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Foreground="Green"/>
        </StackPanel>

        <TextBlock Grid.Row="1" Text="Please provide the following information to create the INI file:" 
                   FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,15"/>

        <!-- Detected Model (Read-only) -->
        <Label Grid.Row="2" Content="Detected PC Model:"/>
        <TextBox Grid.Row="3" x:Name="DetectedModelTextBox" IsReadOnly="True" Background="#F0F0F0"/>

        <!-- OS Type Selection -->
        <Label Grid.Row="4" Content="Operating System Type:"/>
        <ComboBox Grid.Row="5" x:Name="OSTypeComboBox">
            <ComboBoxItem Content="Windows 10 Home"/>
            <ComboBoxItem Content="Windows 10 Home S"/>
            <ComboBoxItem Content="Windows 10 Pro"/>
            <ComboBoxItem Content="Windows 11 Home"/>
            <ComboBoxItem Content="Windows 11 Home S"/>
            <ComboBoxItem Content="Windows 11 Pro"/>
        </ComboBox>

        <!-- Platform Name -->
        <Label Grid.Row="6" Content="Platform Name (e.g., Avid):"/>
        <TextBox Grid.Row="7" x:Name="PlatformNameTextBox" Text="Avid"/>

        <!-- Recovery Pack -->
        <Label Grid.Row="8" Content="Recovery Pack ID (e.g., 653636-031):"/>
        <TextBox Grid.Row="9" x:Name="RecoveryPackTextBox" Text=""/>

        <!-- Buttons -->
        <StackPanel Grid.Row="10" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="CreateINIButton" Content="Create INI File" Click="CreateINIButton_Click" IsDefault="True"/>
            <Button x:Name="SkipButton" Content="Skip" Click="SkipButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
