using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for ModelOverrideDialog.xaml
    /// </summary>
    public partial class ModelOverrideDialog : Window
    {
        public string SelectedFFUPath { get; private set; } = string.Empty;
        public string SelectedModelName { get; private set; } = string.Empty;
        
        private FFUViewModel _viewModel;
        private List<FFUFileInfo> _availableFFUFiles = new List<FFUFileInfo>();
        private List<FFUFileInfo> _filteredFFUFiles = new List<FFUFileInfo>();

        public ModelOverrideDialog(FFUViewModel viewModel)
        {
            try
            {
                InitializeComponent();
                _viewModel = viewModel;

                // Load initial FFU files based on current settings after the dialog is fully loaded
                Loaded += (s, e) =>
                {
                    try
                    {
                        RefreshFFUFilesList();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error during initial FFU files refresh: {ex.Message}\n\nStack trace: {ex.StackTrace}",
                                       "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing Model Override Dialog: {ex.Message}\n\nStack trace: {ex.StackTrace}",
                               "Dialog Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void LocationRadioButton_Checked(object sender, RoutedEventArgs e)
        {
            // Refresh the list when location changes
            RefreshFFUFilesList();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            RefreshFFUFilesList();
        }

        private void RefreshFFUFilesList()
        {
            try
            {
                _availableFFUFiles.Clear();
                _filteredFFUFiles.Clear();

                if (FFUFilesListBox != null)
                    FFUFilesListBox.ItemsSource = null;

                if (NetworkRadioButton?.IsChecked == true)
                {
                    // Scan network location
                    ScanNetworkLocation();
                }
                else if (UsbRadioButton?.IsChecked == true)
                {
                    // Scan USB/external drives
                    ScanUsbLocations();
                }

                // Apply any existing search filter
                ApplySearchFilter(SearchTextBox?.Text ?? string.Empty);

                if (FFUFilesListBox != null)
                    FFUFilesListBox.ItemsSource = _filteredFFUFiles;

                if (SelectedFileInfoTextBlock != null)
                {
                    if (_filteredFFUFiles.Count == 0)
                    {
                        SelectedFileInfoTextBlock.Text = "No FFU images found in the selected location.";
                    }
                    else
                    {
                        SelectedFileInfoTextBlock.Text = $"Found {_filteredFFUFiles.Count} FFU image(s). Select one to continue.";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing FFU files list: {ex.Message}", "Error",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ScanNetworkLocation()
        {
            try
            {
                if (!_viewModel.IsNetworkAvailable)
                {
                    if (SelectedFileInfoTextBlock != null)
                        SelectedFileInfoTextBlock.Text = "Network is not available. Please check network connection.";
                    return;
                }

                // Get the base network path from the view model
                string basePath = _viewModel.GetNetworkBasePath();

                if (string.IsNullOrEmpty(basePath))
                {
                    if (SelectedFileInfoTextBlock != null)
                        SelectedFileInfoTextBlock.Text = "Network base path is not configured.";
                    return;
                }

                if (Directory.Exists(basePath))
                {
                    // Scan all subdirectories for FFU files
                    var directories = Directory.GetDirectories(basePath);

                    foreach (var dir in directories)
                    {
                        if (string.IsNullOrEmpty(dir)) continue;

                        var ffuFiles = Directory.GetFiles(dir, "*.ffu", SearchOption.TopDirectoryOnly);

                        foreach (var ffuFile in ffuFiles)
                        {
                            if (string.IsNullOrEmpty(ffuFile)) continue;

                            var fileInfo = new FileInfo(ffuFile);
                            var fileName = Path.GetFileNameWithoutExtension(ffuFile);
                            var modelName = Path.GetFileName(dir); // Directory name is the model name

                            // Extract disk size from filename if present
                            string diskSize = "Unknown";
                            if (fileName.Contains("_") && fileName.EndsWith("GB"))
                            {
                                var parts = fileName.Split('_');
                                if (parts.Length > 1)
                                {
                                    diskSize = parts[parts.Length - 1];
                                }
                            }

                            if (!string.IsNullOrEmpty(modelName))
                            {
                                _availableFFUFiles.Add(new FFUFileInfo
                                {
                                    ModelName = $"{modelName} ({diskSize})",
                                    FilePath = ffuFile,
                                    FileSize = FormatBytes(fileInfo.Length),
                                    Location = "Network"
                                });
                            }
                        }
                    }
                }
                else
                {
                    if (SelectedFileInfoTextBlock != null)
                        SelectedFileInfoTextBlock.Text = $"Network path does not exist: {basePath}";
                }
            }
            catch (Exception ex)
            {
                if (SelectedFileInfoTextBlock != null)
                    SelectedFileInfoTextBlock.Text = $"Error scanning network location: {ex.Message}";
            }
        }

        private void ScanUsbLocations()
        {
            try
            {
                var usbDrives = _viewModel.GetAvailableUsbDriveInfos();
                foreach (var driveInfo in usbDrives)
                {
                    if (!driveInfo.IsReady) continue;

                    string ffuPath = Path.Combine(driveInfo.Name, "FFU_images");
                    if (Directory.Exists(ffuPath))
                    {
                        var directories = Directory.GetDirectories(ffuPath);
                        foreach (var dir in directories)
                        {
                            if (string.IsNullOrEmpty(dir)) continue;

                            var ffuFiles = Directory.GetFiles(dir, "*.ffu", SearchOption.TopDirectoryOnly);

                            foreach (var ffuFile in ffuFiles)
                            {
                                if (string.IsNullOrEmpty(ffuFile)) continue;

                                var fileInfo = new FileInfo(ffuFile);
                                var fileName = Path.GetFileNameWithoutExtension(ffuFile);
                                var modelName = Path.GetFileName(dir); // Directory name is the model name

                                // Extract disk size from filename if present
                                string diskSize = "Unknown";
                                if (fileName.Contains("_") && fileName.EndsWith("GB"))
                                {
                                    var parts = fileName.Split('_');
                                    if (parts.Length > 1)
                                    {
                                        diskSize = parts[parts.Length - 1];
                                    }
                                }

                                if (!string.IsNullOrEmpty(modelName))
                                {
                                    _availableFFUFiles.Add(new FFUFileInfo
                                    {
                                        ModelName = $"{modelName} ({diskSize})",
                                        FilePath = ffuFile,
                                        FileSize = FormatBytes(fileInfo.Length),
                                        Location = $"USB ({driveInfo.Name})"
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (SelectedFileInfoTextBlock != null)
                    SelectedFileInfoTextBlock.Text = $"Error scanning USB locations: {ex.Message}";
            }
        }

        private void FFUFilesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FFUFilesListBox.SelectedItem is FFUFileInfo selectedFFU)
            {
                SelectedFFUPath = selectedFFU.FilePath;
                SelectedModelName = selectedFFU.ModelName;
                
                SelectedFileInfoTextBlock.Text = $"Selected: {selectedFFU.ModelName}\n" +
                                               $"File: {selectedFFU.FilePath}\n" +
                                               $"Size: {selectedFFU.FileSize}\n" +
                                               $"Location: {selectedFFU.Location}";
                
                SelectButton.IsEnabled = true;
            }
            else
            {
                SelectedFFUPath = string.Empty;
                SelectedModelName = string.Empty;
                SelectedFileInfoTextBlock.Text = "No FFU image selected";
                SelectButton.IsEnabled = false;
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedFFUPath))
            {
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ClearOverrideButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear the model override in the view model
            _viewModel.ClearModelOverride();
            
            MessageBox.Show("Model override has been cleared. Automatic model detection is now active.", 
                           "Override Cleared", MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = false;
            Close();
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }
            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplySearchFilter(SearchTextBox?.Text ?? string.Empty);
        }

        private void ApplySearchFilter(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredFFUFiles = new List<FFUFileInfo>(_availableFFUFiles);
            }
            else
            {
                searchText = searchText.ToLower();
                _filteredFFUFiles = _availableFFUFiles
                    .Where(f => f.ModelName.ToLower().Contains(searchText) || 
                               f.FilePath.ToLower().Contains(searchText))
                    .ToList();
            }

            if (FFUFilesListBox != null)
            {
                FFUFilesListBox.ItemsSource = null;
                FFUFilesListBox.ItemsSource = _filteredFFUFiles;
            }

            if (SelectedFileInfoTextBlock != null)
            {
                if (_filteredFFUFiles.Count == 0)
                {
                    SelectedFileInfoTextBlock.Text = "No matching FFU images found.";
                }
                else
                {
                    SelectedFileInfoTextBlock.Text = $"Found {_filteredFFUFiles.Count} matching FFU image(s). Select one to continue.";
                }
            }
        }
    }

    public class FFUFileInfo
    {
        public string ModelName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileSize { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
    }
}
