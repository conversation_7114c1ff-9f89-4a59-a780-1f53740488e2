using System;
using System.Windows;

namespace FFUCaptureDeploymentTool
{
    public partial class SplashWindow : Window
    {
        public SplashWindow()
        {
            InitializeComponent();
        }

        public void UpdateProgress(int percent, string? message = null)
        {
            Dispatcher.Invoke(() =>
            {
                ProgressBar.Value = Math.Max(0, Math.Min(100, percent));
                PercentText.Text = $"{percent}%";
                if (!string.IsNullOrWhiteSpace(message))
                {
                    StatusText.Text = message;
                }
            });
        }

        public void SetSubtitle(string text)
        {
            Dispatcher.Invoke(() =>
            {
                SubtitleText.Text = text;
            });
        }
    }
}

