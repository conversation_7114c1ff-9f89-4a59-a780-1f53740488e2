@echo off
echo Publishing FFU Capture Deployment Tool for .NET 9.0...

dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeAllContentForSelfExtract=false -p:EnableCompressionInSingleFile=true -p:PublishDir=bin\Release\net9.0-windows\win-x64\publish\single-file

echo.
echo Publishing complete!
echo.
echo Files are available in:
echo - bin\Release\net9.0-windows\win-x64\publish\single-file (.NET Single-File method)
echo.
pause
