{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{304E6351-F05B-4E0C-980A-76E1B741A44C}|FFUCaptureDeploymentTool.csproj|c:\\users\\<USER>\\desktop\\ffucdtool\\postcapturedialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{304E6351-F05B-4E0C-980A-76E1B741A44C}|FFUCaptureDeploymentTool.csproj|solutionrelative:postcapturedialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{304E6351-F05B-4E0C-980A-76E1B741A44C}|FFUCaptureDeploymentTool.csproj|c:\\users\\<USER>\\desktop\\ffucdtool\\ffuviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{304E6351-F05B-4E0C-980A-76E1B741A44C}|FFUCaptureDeploymentTool.csproj|solutionrelative:ffuviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "PostCaptureDialog.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\FFUCDTool\\PostCaptureDialog.xaml", "RelativeDocumentMoniker": "PostCaptureDialog.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\FFUCDTool\\PostCaptureDialog.xaml", "RelativeToolTip": "PostCaptureDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-12T21:05:58.439Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FFUViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\FFUCDTool\\FFUViewModel.cs", "RelativeDocumentMoniker": "FFUViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\FFUCDTool\\FFUViewModel.cs", "RelativeToolTip": "FFUViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHoFAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T14:57:42.815Z", "EditorCaption": ""}]}]}]}