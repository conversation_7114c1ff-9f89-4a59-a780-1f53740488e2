using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace FFUCaptureDeploymentTool
{
    public partial class DiskSelectionDialog : Window
    {
        public int SelectedDiskNumber { get; private set; }

        public DiskSelectionDialog(IEnumerable<string> availableDisks, int currentDiskNumber)
        {
            InitializeComponent();

            // Populate the list box with available disks
            foreach (var disk in availableDisks)
            {
                DiskListBox.Items.Add(disk);
            }

            // Select the current disk
            bool diskFound = false;
            for (int i = 0; i < DiskListBox.Items.Count; i++)
            {
                string diskItem = DiskListBox.Items[i].ToString() ?? string.Empty;
                if (diskItem.StartsWith($"Disk {currentDiskNumber}:"))
                {
                    DiskListBox.SelectedIndex = i;
                    diskFound = true;
                    break;
                }
            }

            // If no disk is selected, select the first one
            if (DiskListBox.SelectedIndex == -1 && DiskListBox.Items.Count > 0)
            {
                DiskListBox.SelectedIndex = 0;
            }

            // Initialize the selected disk number based on what's actually selected
            if (diskFound)
            {
                SelectedDiskNumber = currentDiskNumber;
            }
            else
            {
                // Extract disk number from the first selected item
                if (DiskListBox.SelectedItem != null)
                {
                    string selectedDisk = DiskListBox.SelectedItem.ToString() ?? string.Empty;
                    if (selectedDisk.StartsWith("Disk "))
                    {
                        int endIndex = selectedDisk.IndexOf(':', 5);
                        if (endIndex > 5)
                        {
                            string diskNumberStr = selectedDisk.Substring(5, endIndex - 5);
                            if (int.TryParse(diskNumberStr, out int diskNumber))
                            {
                                SelectedDiskNumber = diskNumber;
                            }
                        }
                    }
                }
                else
                {
                    SelectedDiskNumber = 0; // Default fallback
                }
            }
        }

        private void DiskListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DiskListBox.SelectedItem != null)
            {
                string selectedDisk = DiskListBox.SelectedItem.ToString() ?? string.Empty;

                // Extract the disk number from the selected item
                // Format is "Disk X: ..."
                if (selectedDisk.StartsWith("Disk "))
                {
                    int endIndex = selectedDisk.IndexOf(':', 5);
                    if (endIndex > 5)
                    {
                        string diskNumberStr = selectedDisk.Substring(5, endIndex - 5);
                        if (int.TryParse(diskNumberStr, out int diskNumber))
                        {
                            SelectedDiskNumber = diskNumber;
                        }
                    }
                }
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // Converter to convert null to false and non-null to true
    public class NullToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            return value != null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
