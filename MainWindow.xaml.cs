﻿using System.Windows;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private FFUViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = new FFUViewModel();
            DataContext = _viewModel;

            // Initialize the application
            _viewModel.Initialize();

            // Set the window title to include the contract
            this.Title = $"Windows Disk (FFU) Capture/Deployment Tool - v0.5b - {_viewModel.Contract}";

            // Subscribe to log event to auto-scroll
            _viewModel.LogUpdated += (s, e) =>
            {
                // Use the ScrollViewer to scroll to the bottom
                LogScrollViewer.ScrollToEnd();
            };
        }

        private void RevokeNetworkAccess_Click(object sender, RoutedEventArgs e)
        {
            // Call the RevokeNetworkAccess method on the view model
            _viewModel.RevokeNetworkAccess();

            // Show a confirmation message
            MessageBox.Show(
                "Network access has been revoked. All network connections have been closed and credentials have been cleared.",
                "Network Access Revoked",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
    }
}