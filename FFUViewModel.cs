﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Diagnostics.Contracts;
using System.IO;
using System.Net.NetworkInformation;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace FFUCaptureDeploymentTool
{
    public class FFUViewModel : INotifyPropertyChanged
    {
        #region Properties
        private string _pcModel = string.Empty; // Initialize with empty string
        public string PcModel
        {
            get => _pcModel;
            set => SetProperty(ref _pcModel, value);
        }

        private string _cfgModel = "UNKNOWN";
        public string CfgModel
        {
            get => _cfgModel;
            set => SetProperty(ref _cfgModel, value);
        }

        private string _osType = "UNKNOWN";
        public string OsType
        {
            get => _osType;
            set => SetProperty(ref _osType, value);
        }



        private bool _isNetworkAvailable;
        public bool IsNetworkAvailable
        {
            get => _isNetworkAvailable;
            set => SetProperty(ref _isNetworkAvailable, value);
        }

        private bool _isUsbDriveAvailable;
        public bool IsUsbDriveAvailable
        {
            get => _isUsbDriveAvailable;
            set => SetProperty(ref _isUsbDriveAvailable, value);
        }

        private bool _useUsbDrive;
        public bool UseUsbDrive
        {
            get => _useUsbDrive;
            set => SetProperty(ref _useUsbDrive, value);
        }

        private string _selectedUsbDrive = string.Empty;
        public string SelectedUsbDrive
        {
            get => _selectedUsbDrive;
            set => SetProperty(ref _selectedUsbDrive, value);
        }

        private ObservableCollection<string> _availableUsbDrives = new ObservableCollection<string>();
        public ObservableCollection<string> AvailableUsbDrives
        {
            get => _availableUsbDrives;
            set => SetProperty(ref _availableUsbDrives, value);
        }

        private int _totalDeploymentCount;
        public int TotalDeploymentCount
        {
            get => _totalDeploymentCount;
            set => SetProperty(ref _totalDeploymentCount, value);
        }

        private string _logContent = "";
        public string LogContent
        {
            get => _logContent;
            set => SetProperty(ref _logContent, value);
        }

        private bool _isOperationInProgress;
        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            set
            {
                if (SetProperty(ref _isOperationInProgress, value))
                {
                    // When operation state changes, force command availability to be re-evaluated
                    CaptureImageCommand.RaiseCanExecuteChanged();
                    DeployImageCommand.RaiseCanExecuteChanged();
                    CheckNetworkCommand.RaiseCanExecuteChanged();
                    ViewHistoryCommand.RaiseCanExecuteChanged();
                    CredentialsCommand.RaiseCanExecuteChanged();
                    SelectUsbDriveCommand.RaiseCanExecuteChanged();
                    RefreshUsbDrivesCommand.RaiseCanExecuteChanged();
                    SelectDiskCommand.RaiseCanExecuteChanged();
                    SettingsCommand.RaiseCanExecuteChanged();
                    ExitCommand.RaiseCanExecuteChanged();
                }
            }
        }

        private int _selectedDiskNumber;
        public int SelectedDiskNumber
        {
            get => _selectedDiskNumber;
            set
            {
                if (SetProperty(ref _selectedDiskNumber, value))
                {
                    // Update disk size when disk number changes
                    UpdateSelectedDiskSize();
                }
            }
        }

        private string _selectedDiskSize = string.Empty;
        public string SelectedDiskSize
        {
            get => _selectedDiskSize;
            set => SetProperty(ref _selectedDiskSize, value);
        }

        private bool _isModelOverrideActive = false;
        public bool IsModelOverrideActive
        {
            get => _isModelOverrideActive;
            set => SetProperty(ref _isModelOverrideActive, value);
        }

        private string _overrideFFUFilePath = string.Empty;
        public string OverrideFFUFilePath
        {
            get => _overrideFFUFilePath;
            set => SetProperty(ref _overrideFFUFilePath, value);
        }

        private string _overrideModelName = string.Empty;
        public string OverrideModelName
        {
            get => _overrideModelName;
            set => SetProperty(ref _overrideModelName, value);
        }

        private ObservableCollection<string> _availableDisks = new ObservableCollection<string>();
        public ObservableCollection<string> AvailableDisks
        {
            get => _availableDisks;
            set => SetProperty(ref _availableDisks, value);
        }

        private string _contract = "HP"; // Default to HP
        public string Contract => _contract;
        #endregion

        #region Private Fields
        private string _diskpartScript = string.Empty;
        private string _imagePath = string.Empty;
        private string _ffuFile = string.Empty;
        private string _logFile = string.Empty;
        private string _iniFile = string.Empty;
        private string _networkServer = string.Empty;
        private string _imageBasePath = string.Empty;
        private string _usbDrivePath = string.Empty;
        private string _networkUsername = string.Empty;
        private string _networkPassword = string.Empty;
        private bool _useNetworkCredentials = false;
        private bool _showLogging = true; // Default to showing logs
        // Disk number is defined in the properties section
        private string _configFile = string.Empty; // Will be set by GetConfigFilePath()

        // Timer for monitoring USB drive availability
        private DispatcherTimer? _usbDriveMonitorTimer;
        #endregion

        #region Commands
        public RelayCommand CaptureImageCommand { get; private set; }
        public RelayCommand DeployImageCommand { get; private set; }
        public RelayCommand CheckNetworkCommand { get; private set; }
        public RelayCommand ViewHistoryCommand { get; private set; }
        public RelayCommand CredentialsCommand { get; private set; }
        public RelayCommand SelectUsbDriveCommand { get; private set; }
        public RelayCommand RefreshUsbDrivesCommand { get; private set; }
        public RelayCommand SelectDiskCommand { get; private set; }
        public RelayCommand ModelOverrideCommand { get; private set; }
        public RelayCommand SettingsCommand { get; private set; }
        public RelayCommand ExitCommand { get; private set; }
        #endregion

        #region Constructor
        public FFUViewModel()
        {
            // Initialize commands
            CaptureImageCommand = new RelayCommand(CaptureImage, CanExecuteOperation);
            DeployImageCommand = new RelayCommand(DeployImage, CanExecuteOperation);
            CheckNetworkCommand = new RelayCommand(CheckNetworkAccess, CanExecuteAnyTime);
            ViewHistoryCommand = new RelayCommand(ViewDeploymentHistory, CanExecuteAnyTime);
            CredentialsCommand = new RelayCommand(ShowCredentialsDialog, CanExecuteAnyTime);
            SelectUsbDriveCommand = new RelayCommand(ShowUsbDriveDialog, CanExecuteAnyTime);
            RefreshUsbDrivesCommand = new RelayCommand(RefreshUsbDrives, CanExecuteAnyTime);
            SelectDiskCommand = new RelayCommand(ShowDiskSelectionDialog, CanExecuteAnyTime);
            ModelOverrideCommand = new RelayCommand(ShowModelOverrideDialog, CanExecuteAnyTime);
            SettingsCommand = new RelayCommand(ShowSettingsDialog, CanExecuteAnyTime);
            ExitCommand = new RelayCommand(ExitApplication, CanExecuteAnyTime);
        }
        #endregion

        #region Initialization Methods
        public void Initialize()
        {
            Console.WriteLine("Starting application initialization...");
            
            LoadConfig();
            Console.WriteLine("Configuration loaded.");
            
            InitializeVariables();
            Console.WriteLine("Variables initialized.");

            // Skip automatic network check for faster startup
            // Network will be checked only when user clicks "Check Network" button
            IsNetworkAvailable = false;
            Console.WriteLine("Network check skipped - will connect on demand.");

            RefreshUsbDrives();
            Console.WriteLine("USB drives refreshed.");
            
            RefreshAvailableDisks();
            Console.WriteLine("Available disks refreshed.");

            // Now that disks are enumerated, validate and update the selected disk
            ValidateAndUpdateSelectedDisk();
            Console.WriteLine($"Disk validation completed for selected disk: {SelectedDiskNumber}");

            ReadConfigIfExists();
            Console.WriteLine("INI config read if exists.");
            
            LoadDeploymentCount();
            Console.WriteLine("Deployment count loaded.");

            // Set up the USB drive monitor timer
            SetupUsbDriveMonitor();
            Console.WriteLine("USB drive monitor set up.");

            Log($"Application initialized. Ready for operation.");
            Console.WriteLine("Application initialization completed.");
        }

        private void SetupUsbDriveMonitor()
        {
            // Create a timer to periodically check USB drive availability
            _usbDriveMonitorTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(10) // Check every 10 seconds
            };

            // Set up the timer event handler
            _usbDriveMonitorTimer.Tick += (sender, e) =>
            {
                // Only check if we're in USB drive mode
                if (_useUsbDrive)
                {
                    // Store the current state
                    bool wasAvailable = IsUsbDriveAvailable;

                    // Check if the drive is still available
                    if (!string.IsNullOrEmpty(_usbDrivePath))
                    {
                        try
                        {
                            DriveInfo drive = new DriveInfo(_usbDrivePath);
                            bool isNowAvailable = drive.IsReady;

                            // If the state has changed, update and log
                            if (wasAvailable && !isNowAvailable)
                            {
                                // Drive was available but is now disconnected
                                Log($"WARNING: External drive {_selectedUsbDrive} has been disconnected.", true);
                                Log("Use 'Check Network' button to connect to network if needed.", true);

                                // Refresh drives to update status
                                RefreshUsbDrives();
                            }
                            else if (!wasAvailable && isNowAvailable)
                            {
                                // Drive was disconnected but is now available
                                Log($"External drive {_selectedUsbDrive} is now connected.", true);

                                // Refresh drives to update status
                                RefreshUsbDrives();
                            }
                        }
                        catch
                        {
                            // If we get an exception, the drive is likely not available
                            if (wasAvailable)
                            {
                                Log($"WARNING: External drive {_selectedUsbDrive} is no longer accessible.", true);

                                // Refresh drives to update status and potentially switch to network mode
                                RefreshUsbDrives();
                            }
                        }
                    }
                }
            };

            // Start the timer
            _usbDriveMonitorTimer.Start();

            Log("USB drive monitoring started.");
        }

        private void SaveConfig()
        {
            try
            {
                // Use the config file path that was determined during LoadConfig
                if (string.IsNullOrEmpty(_configFile))
                {
                    _configFile = GetConfigFilePath();
                }
                
                // Build the config file content
                var configContent = new System.Text.StringBuilder();
                configContent.AppendLine("# FFU Capture/Deployment Tool Configuration File");
                configContent.AppendLine();
                configContent.AppendLine("# Contract");
                configContent.AppendLine($"Contract={_contract}");
                configContent.AppendLine();
                configContent.AppendLine("# Network Settings");
                configContent.AppendLine($"ServerIP={_networkServer}");
                configContent.AppendLine($"ImageBasePath={_imageBasePath}");
                configContent.AppendLine();
                configContent.AppendLine("# USB Drive Settings");
                configContent.AppendLine($"UseUsbDrive={_useUsbDrive.ToString().ToLower()}");
                configContent.AppendLine($"UsbDrivePath={_usbDrivePath}");
                configContent.AppendLine($"SelectedUsbDrive={_selectedUsbDrive}");
                configContent.AppendLine();
                configContent.AppendLine("# Network Credentials");
                configContent.AppendLine($"NetworkUsername={ObfuscateCredential(_networkUsername)}");
                configContent.AppendLine($"NetworkPassword={ObfuscateCredential(_networkPassword)}");
                configContent.AppendLine();
                configContent.AppendLine("# Disk Settings");
                configContent.AppendLine($"SelectedDiskNumber={_selectedDiskNumber}");
                configContent.AppendLine();
                configContent.AppendLine("# UI Settings");
                configContent.AppendLine($"ShowLogging={_showLogging.ToString().ToLower()}");

                // Ensure the directory exists
                string? directory = Path.GetDirectoryName(_configFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Write to the config file
                File.WriteAllText(_configFile, configContent.ToString());

                Console.WriteLine($"Configuration saved successfully to: {_configFile}");

                if (_showLogging)
                {
                    Log("Configuration saved successfully.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving configuration: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (_showLogging)
                {
                    Log($"Error saving configuration: {ex.Message}", true);
                }
            }
        }

        private void LoadConfig()
        {
            // First, ensure we have a valid config file path
            _configFile = GetConfigFilePath();
            
            try
            {
                // Log the config file path for debugging
                Console.WriteLine($"Attempting to load config from: {_configFile}");
                
                if (!File.Exists(_configFile))
                {
                    Console.WriteLine("Config file not found, creating default configuration.");
                    _networkServer = "***********";
                    _imageBasePath = $@"\\{_networkServer}\FFU_images\";
                    UseUsbDrive = false;
                    _usbDrivePath = "";
                    _contract = "HP";
                    _showLogging = true; // Set default logging to true
                    
                    // Create a default config file
                    SaveConfig();
                    Console.WriteLine("Default config file created successfully.");
                    return;
                }

                Console.WriteLine("Config file found, reading configuration...");
                var configLines = File.ReadAllLines(_configFile);
                Console.WriteLine($"Read {configLines.Length} lines from config file.");
                
                // First pass: Load ShowLogging setting to enable/disable logging for the rest of the process
                bool showLoggingFound = false;
                foreach (var line in configLines)
                {
                    if (line.Trim().StartsWith("ShowLogging="))
                    {
                        string[] parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            string value = parts[1].Trim().ToLower();
                            _showLogging = value == "true" || value == "1" || value == "yes";
                            showLoggingFound = true;
                            Console.WriteLine($"ShowLogging setting loaded: {_showLogging}");
                            break;
                        }
                    }
                }
                
                // If ShowLogging wasn't found, set default
                if (!showLoggingFound)
                {
                    _showLogging = true;
                    Console.WriteLine("ShowLogging setting not found, using default: true");
                }

                // Second pass: Load all other settings
                foreach (var line in configLines)
                {
                    string[] parts;

                    if (line.Trim().StartsWith("ServerIP="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            _networkServer = parts[1].Trim();
                            Console.WriteLine($"ServerIP loaded: {_networkServer}");
                        }
                    }
                    else if (line.Trim().StartsWith("ImageBasePath="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            _imageBasePath = parts[1].Trim();
                            _imageBasePath = _imageBasePath.Replace("{ServerIP}", _networkServer);
                            Console.WriteLine($"ImageBasePath loaded: {_imageBasePath}");
                        }
                    }
                    else if (line.Trim().StartsWith("NetworkUsername="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            string encryptedUsername = parts[1].Trim();
                            _networkUsername = DeobfuscateCredential(encryptedUsername);
                            _useNetworkCredentials = !string.IsNullOrEmpty(_networkUsername);
                            Console.WriteLine($"NetworkUsername loaded: {(_useNetworkCredentials ? "***" : "not set")}");
                        }
                    }
                    else if (line.Trim().StartsWith("NetworkPassword="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            string encryptedPassword = parts[1].Trim();
                            _networkPassword = DeobfuscateCredential(encryptedPassword);
                            Console.WriteLine("NetworkPassword loaded: ***");
                        }
                    }
                    else if (line.Trim().StartsWith("UseUsbDrive="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            string value = parts[1].Trim().ToLower();
                            UseUsbDrive = value == "true" || value == "1" || value == "yes";
                            Console.WriteLine($"UseUsbDrive loaded: {UseUsbDrive}");
                        }
                    }
                    else if (line.Trim().StartsWith("UsbDrivePath="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            _usbDrivePath = parts[1].Trim();
                            Console.WriteLine($"UsbDrivePath loaded: {_usbDrivePath}");
                        }
                    }
                    else if (line.Trim().StartsWith("SelectedUsbDrive="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            SelectedUsbDrive = parts[1].Trim();
                            Console.WriteLine($"SelectedUsbDrive loaded: {SelectedUsbDrive}");
                        }
                    }
                    else if (line.Trim().StartsWith("SelectedDiskNumber="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1 && int.TryParse(parts[1].Trim(), out int diskNumber))
                        {
                            // Set the backing field directly to avoid triggering UpdateSelectedDiskSize during initialization
                            _selectedDiskNumber = diskNumber;
                            Console.WriteLine($"SelectedDiskNumber loaded: {_selectedDiskNumber}");
                        }
                    }
                    else if (line.Trim().StartsWith("Contract="))
                    {
                        parts = line.Split('=', 2);
                        if (parts.Length > 1)
                        {
                            _contract = parts[1].Trim();
                            Console.WriteLine($"Contract loaded: {_contract}");
                        }
                    }
                }

                // Set defaults for missing values
                if (string.IsNullOrEmpty(_networkServer))
                {
                    _networkServer = "***********";
                    Console.WriteLine("Warning: ServerIP not found in config, using default: " + _networkServer);
                }
                if (string.IsNullOrEmpty(_imageBasePath))
                {
                    _imageBasePath = $@"\\{_networkServer}\burnin\.logs\FFU_images\";
                    Console.WriteLine("Warning: ImageBasePath not found in config, using default: " + _imageBasePath);
                }
                
                Console.WriteLine("Configuration loaded successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading config file: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                // Set safe defaults
                _networkServer = "***********";
                _imageBasePath = $@"\\{_networkServer}\burnin\.logs\FFU_images\";
                _showLogging = true;
                UseUsbDrive = false;
                _usbDrivePath = "";
                _contract = "HP";
            }
        }

        private string GetConfigFilePath()
        {
            try
            {
                // Try multiple approaches to get the correct base directory
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string configPath = Path.Combine(baseDir, "config.cfg");
                
                Console.WriteLine($"Base directory: {baseDir}");
                Console.WriteLine($"Config path: {configPath}");
                
                // If the config file exists at this path, use it
                if (File.Exists(configPath))
                {
                    Console.WriteLine("Config file found at base directory path.");
                    return configPath;
                }
                
                // Try the current working directory
                string currentDir = Environment.CurrentDirectory;
                string currentDirConfigPath = Path.Combine(currentDir, "config.cfg");
                Console.WriteLine($"Current directory: {currentDir}");
                Console.WriteLine($"Current directory config path: {currentDirConfigPath}");
                
                if (File.Exists(currentDirConfigPath))
                {
                    Console.WriteLine("Config file found at current directory path.");
                    return currentDirConfigPath;
                }
                
                // Try the executable directory
                string exeDir = AppContext.BaseDirectory;
                string exeDirConfigPath = Path.Combine(exeDir, "config.cfg");
                Console.WriteLine($"Executable directory: {exeDir}");
                Console.WriteLine($"Executable directory config path: {exeDirConfigPath}");
                
                if (File.Exists(exeDirConfigPath))
                {
                    Console.WriteLine("Config file found at executable directory path.");
                    return exeDirConfigPath;
                }
                

                
                // If no config file found, use the base directory as the target location
                Console.WriteLine("No existing config file found, will create at base directory.");
                return configPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error determining config file path: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                // Fallback to base directory
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.cfg");
            }
        }

        private void InitializeVariables()
        {
            PcModel = GetComputerModel();
            PcModel = PcModel.Replace(" ", "").Replace("/", "").Replace("\\", "");

            Random random = new Random();
            _diskpartScript = Path.Combine(Path.GetTempPath(), $"diskpart_{random.Next(1000, 9999)}.txt");

            // Get disk size in GB from SelectedDiskSize
            string diskSizeGB = "Unknown";
            if (SelectedDiskSize.StartsWith("Size: "))
            {
                diskSizeGB = SelectedDiskSize.Substring(6).Replace("GB", "").Trim();
            }

            // When model override is active, don't create model-specific folders
            // Instead, use a generic path and log the actual FFU file used
            if (IsModelOverrideActive)
            {
                // Use a generic "Override" folder to avoid creating unnecessary model folders
                if (_useUsbDrive && !string.IsNullOrEmpty(_usbDrivePath))
                {
                    _imagePath = Path.Combine(_usbDrivePath, "FFU_images", "Override");
                }
                else
                {
                    _imagePath = Path.Combine(_imageBasePath, "Override");
                }

                // For override mode, create generic log files that will track the actual FFU used
                _ffuFile = Path.Combine(_imagePath, $"Override_{diskSizeGB}GB.ffu");
                _logFile = Path.Combine(_imagePath, $"Override_{diskSizeGB}GB_deploy_log.txt");
                _iniFile = Path.Combine(_imagePath, $"Override_{diskSizeGB}GB.ini");
            }
            else
            {
                // Normal mode: use detected model for folder and file names
                if (_useUsbDrive && !string.IsNullOrEmpty(_usbDrivePath))
                {
                    _imagePath = Path.Combine(_usbDrivePath, "FFU_images", PcModel);
                }
                else
                {
                    _imagePath = Path.Combine(_imageBasePath, PcModel);
                }

                _ffuFile = Path.Combine(_imagePath, $"{PcModel}_{diskSizeGB}GB.ffu");
                _logFile = Path.Combine(_imagePath, $"{PcModel}_{diskSizeGB}GB_deploy_log.txt");
                _iniFile = Path.Combine(_imagePath, $"{PcModel}_{diskSizeGB}GB.ini");
            }
        }
        #endregion

        #region Network Methods
        private bool CheckNetworkServerReachable()
        {
            try
            {
                using (Ping ping = new Ping())
                {
                    PingReply reply = ping.Send(_networkServer, 2000);
                    return (reply != null && reply.Status == IPStatus.Success);
                }
            }
            catch
            {
                return false;
            }
        }

        private bool ConnectToNetworkShare()
        {
            if (!_useNetworkCredentials)
            {
                // No credentials needed, just check if network is reachable
                return CheckNetworkServerReachable();
            }

            try
            {
                // Disconnect any existing connections first to avoid "multiple connections" error
                ExecuteProcess("net", $"use \\\\{_networkServer} /delete /y");

                // Connect with credentials
                bool success = ExecuteProcess("net", $"use \\\\{_networkServer} /user:{_networkUsername} {_networkPassword}", true, true);

                if (success)
                {
                    Log($"Successfully authenticated to network share.");
                    return true;
                }
                else
                {
                    Log($"Failed to authenticate to network share. Check credentials.", true);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log($"Error connecting to network share: {ex.Message}", true);
                return false;
            }
        }

        // Method to update network credentials
        public void UpdateNetworkCredentials(string username, string password)
        {
            _networkUsername = username;
            _networkPassword = password;
            _useNetworkCredentials = !string.IsNullOrEmpty(_networkUsername);

            // Save the updated credentials to the config file
            SaveConfig();

            // Automatically connect when credentials are provided
            Log("Credentials updated. Attempting to connect to network...");
            CheckNetworkAccess();
        }

        private void CheckNetworkAccess()
        {
            Log("Connecting to network...");

            try
            {
                // First check if server is reachable
                Log($"Checking if server {_networkServer} is reachable...");
                bool serverReachable = CheckNetworkServerReachable();

                if (!serverReachable)
                {
                    IsNetworkAvailable = false;
                    Log($"ERROR: Network server {_networkServer} is not reachable.", true);
                    Log("Please check network connection and server IP address.", true);
                    return;
                }

                Log("Server is reachable. Establishing connection...");

                // If we need credentials, connect with them
                if (_useNetworkCredentials)
                {
                    Log("Authenticating with network credentials...");
                    IsNetworkAvailable = ConnectToNetworkShare();
                    if (!IsNetworkAvailable)
                    {
                        Log("Network authentication failed.", true);
                        return;
                    }
                    Log("Network authentication successful.");
                }
                else
                {
                    Log("No credentials required for network access.");
                    IsNetworkAvailable = true;
                }

                // Now check if the path is accessible
                string? parentPath = Path.GetDirectoryName(_imagePath);
                if (string.IsNullOrEmpty(parentPath))
                {
                    IsNetworkAvailable = false;
                    Log($"ERROR: Invalid path: {_imagePath}", true);
                    return;
                }

                Log($"Checking network path accessibility: {parentPath}");
                if (!Directory.Exists(parentPath))
                {
                    try
                    {
                        Log("Creating network directory...");
                        Directory.CreateDirectory(parentPath);
                        Log("Network directory created successfully.");
                    }
                    catch (Exception ex)
                    {
                        IsNetworkAvailable = false;
                        Log($"ERROR: Cannot access or create path: {parentPath}", true);
                        Log($"Error details: {ex.Message}", true);
                        Log("Check permissions or network configuration.", true);
                        return;
                    }
                }

                Log($"✓ Network connection established successfully!");
                Log($"✓ Network location accessible: {_imagePath}");

                // Update UI to reflect network availability
                OnPropertyChanged(nameof(IsNetworkAvailable));
            }
            catch (Exception ex)
            {
                IsNetworkAvailable = false;
                Log($"ERROR establishing network connection: {ex.Message}", true);
                Log("Please check network settings and try again.", true);
            }
        }
        #endregion

        #region Utility Methods
        private string GetComputerModel()
        {
            try
            {
                string wmicArg = "ComputerSystem GET Model";
                if (_contract.Equals("HP", StringComparison.OrdinalIgnoreCase))
                {
                    wmicArg = "ComputerSystem GET SystemSKUnumber"; //SystemSKUnumber for HP
                }
                else if (_contract.Equals("Lenovo", StringComparison.OrdinalIgnoreCase))
                {
                    wmicArg = "ComputerSystem GET Model"; //Model for Lenovo
                }
                Process process = new()
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "wmic",
                        Arguments = wmicArg,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                string output = process.StandardOutput.ReadToEnd().Trim();
                process.WaitForExit();

                var lines = output.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                                  .Select(line => line.Trim())
                                  .ToList();

                if (lines.Count > 1)
                {
                    return lines[1];
                }
                else
                {
                    Log("SKU number could not be determined, returning UNKNOWN", true);
                    return "UNKNOWN";
                }
            }
            catch (Exception ex)
            {
                Log($"Error getting computer model: {ex.Message}", true);
            }

            Log("SKU number could not be determined, returning UNKNOWN", true);
            return "UNKNOWN";
        }

        private void ReadConfigIfExists()
        {
            // Check if the INI file exists, regardless of whether we're using network or USB drive
            bool shouldReadIniFile = false;

            if (_useUsbDrive && IsUsbDriveAvailable)
            {
                // When using USB drive, check if the file exists
                shouldReadIniFile = File.Exists(_iniFile);
                if (shouldReadIniFile)
                {
                    Log($"Found INI file on USB drive: {_iniFile}");
                }
            }
            else if (IsNetworkAvailable)
            {
                // When using network, check if the file exists
                shouldReadIniFile = File.Exists(_iniFile);
                if (shouldReadIniFile)
                {
                    Log($"Found INI file on network: {_iniFile}");
                }
            }

            if (shouldReadIniFile)
            {
                try
                {
                    string[] config = File.ReadAllText(_iniFile).Split(';');
                    CfgModel = config[0];
                    OsType = config.Length > 1 ? config[1] : "UNKNOWN";
                    Log($"Successfully read INI file. Model: {CfgModel}, OS: {OsType}");
                }
                catch (Exception ex)
                {
                    Log($"Error reading config file: {ex.Message}", true);
                }
            }
            else
            {
                Log($"No INI file found at: {_iniFile}");
            }
        }

        private void LoadDeploymentCount()
        {
            // Load deployment count from log file
            TotalDeploymentCount = GetOperationCount("Deployment");
        }

        private bool ExecuteProcess(string program, string arguments, bool logOutput = true, bool containsCredentials = false)
        {
            try
            {
                if (logOutput)
                {
                    // If the command contains credentials, mask them in the log
                    string logSafeArguments = arguments;
                    if (containsCredentials)
                    {
                        // Simple masking for any parameter that might contain credentials
                        logSafeArguments = arguments.Replace(_networkUsername, "***").Replace(_networkPassword, "***");

                        // More thorough masking using regex to catch credentials in various formats
                        if (!string.IsNullOrEmpty(_networkUsername))
                        {
                            logSafeArguments = System.Text.RegularExpressions.Regex.Replace(
                                logSafeArguments,
                                $"/user:[^\\s]+",
                                "/user:***");
                        }
                    }

                    Log($"Executing: {program} {logSafeArguments}");
                }

                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = program,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (Process process = new Process { StartInfo = psi })
                {
                    process.Start();
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (!string.IsNullOrEmpty(output) && logOutput)
                    {
                        Log(output);
                    }

                    if (process.ExitCode != 0)
                    {
                        if (!string.IsNullOrEmpty(error) && logOutput)
                        {
                            Log($"Process error: {error}", true);
                        }
                        return false;
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                if (logOutput)
                {
                    Log($"Error executing process: {ex.Message}", true);
                }
                return false;
            }
        }

        private async Task<bool> ExecuteProcessWithRealTimeOutput(string program, string arguments, bool logOutput = true, bool containsCredentials = false, ProgressDialog? progressDialog = null, CancellationToken cancellationToken = default)
        {
            try
            {
                if (logOutput && _showLogging) // Only log if both logOutput and _showLogging are true
                {
                    // If the command contains credentials, mask them in the log
                    string logSafeArguments = arguments;
                    if (containsCredentials)
                    {
                        // Simple masking for any parameter that might contain credentials
                        logSafeArguments = arguments.Replace(_networkUsername, "***").Replace(_networkPassword, "***");

                        // More thorough masking using regex to catch credentials in various formats
                        if (!string.IsNullOrEmpty(_networkUsername))
                        {
                            logSafeArguments = System.Text.RegularExpressions.Regex.Replace(
                                logSafeArguments,
                                $"/user:[^\\s]+",
                                "/user:***");
                        }
                    }

                    Log($"Executing: {program} {logSafeArguments}");
                }

                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = program,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (Process process = new Process { StartInfo = psi })
                {
                    // Set up output handling to capture in real-time
                    var outputBuilder = new System.Text.StringBuilder();
                    var errorBuilder = new System.Text.StringBuilder();
                    var lastLine = "";
                    double lastPercentage = 0;

                    process.OutputDataReceived += (sender, e) =>
                    {
                        if (e.Data != null)
                        {
                            // Skip empty lines
                            if (string.IsNullOrWhiteSpace(e.Data))
                                return;

                            // Add to our complete output
                            outputBuilder.AppendLine(e.Data);

                            if (logOutput)
                            {
                                // Only log if it's not a duplicate progress line
                                bool isProgressLine = e.Data.Contains("%") && System.Text.RegularExpressions.Regex.IsMatch(e.Data, "\\d+\\.?\\d*%");

                                // Always log progress lines regardless of ShowLogging setting
                                if (isProgressLine)
                                {
                                    var currentMatch = System.Text.RegularExpressions.Regex.Match(e.Data, "(\\d+\\.?\\d*)%");
                                    var lastMatch = System.Text.RegularExpressions.Regex.Match(lastLine, "(\\d+\\.?\\d*)%");

                                    // Update progress dialog with simple percentage
                                    if (progressDialog != null && currentMatch.Success)
                                    {
                                        if (double.TryParse(currentMatch.Groups[1].Value, out double percentage))
                                        {
                                            // Only update if percentage has changed significantly (avoid bouncing)
                                            if (Math.Abs(percentage - lastPercentage) >= 1.0)
                                            {
                                                progressDialog.UpdateProgress(percentage, e.Data.Trim());
                                                lastPercentage = percentage;
                                            }
                                        }
                                    }

                                    // Only log if the percentage has changed
                                    if (currentMatch.Success && (!lastMatch.Success ||
                                        currentMatch.Groups[1].Value != lastMatch.Groups[1].Value))
                                    {
                                        Application.Current.Dispatcher.Invoke(() =>
                                        {
                                            Log(e.Data);
                                        });
                                    }
                                }
                                // For non-progress lines, only log if ShowLogging is enabled
                                else if (_showLogging && (!isProgressLine || string.IsNullOrEmpty(lastLine)))
                                {
                                    Application.Current.Dispatcher.Invoke(() =>
                                    {
                                        Log(e.Data);
                                    });
                                }

                                lastLine = e.Data;
                            }
                        }
                    };

                    process.ErrorDataReceived += (sender, e) =>
                    {
                        if (e.Data != null)
                        {
                            errorBuilder.AppendLine(e.Data);
                            if (logOutput && _showLogging) // Only log errors if ShowLogging is enabled
                            {
                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    Log($"Error: {e.Data}", true);
                                });
                            }
                        }
                    };

                    process.Start();
                    process.BeginOutputReadLine();
                    process.BeginErrorReadLine();

                    // Wait for the process to exit asynchronously with cancellation support
                    await Task.Run(() =>
                    {
                        while (!process.WaitForExit(1000)) // Check every second
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                try
                                {
                                    process.Kill();
                                    process.WaitForExit(5000); // Wait up to 5 seconds for graceful exit
                                }
                                catch (Exception ex)
                                {
                                    if (logOutput && _showLogging)
                                    {
                                        Log($"Error killing process: {ex.Message}", true);
                                    }
                                }
                                cancellationToken.ThrowIfCancellationRequested();
                            }
                        }
                    }, cancellationToken);

                    if (process.ExitCode != 0)
                    {
                        if (errorBuilder.Length > 0 && logOutput && _showLogging)
                        {
                            Log($"Process completed with errors (Exit code: {process.ExitCode})", true);
                        }
                        return false;
                    }

                    if (logOutput && _showLogging)
                    {
                        Log("Process completed successfully");
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                if (logOutput && _showLogging)
                {
                    Log($"Error executing process: {ex.Message}", true);
                }
                return false;
            }
        }

        private void UpdateLog(string operation = "Deployment")
        {
            try
            {
                // Ensure the directory exists before trying to create or update the log file
                string? directory = Path.GetDirectoryName(_logFile);
                if (string.IsNullOrEmpty(directory))
                {
                    Log($"Error: Invalid log file path: {_logFile}", true);
                    return;
                }

                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                int count = GetOperationCount(operation);
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                string computerName = Environment.MachineName;
                string userName = Environment.UserName;

                // Determine what to log based on whether model override is active
                string logHeader;
                string logEntry;

                if (IsModelOverrideActive && !string.IsNullOrEmpty(OverrideFFUFilePath))
                {
                    // Log the actual FFU file used and detected model for reference
                    logHeader = $"Log for Override FFU Deployment (Detected Model: {PcModel})\r\n";
                    logEntry = $"{operation} on {timestamp} - {operation} #{count + 1} by {userName} on {computerName} - FFU: {Path.GetFileName(OverrideFFUFilePath)} (Override: {OverrideModelName})\r\n";
                }
                else
                {
                    // Normal logging for auto-detected model
                    logHeader = $"Log for {PcModel} FFU Image\r\n";
                    logEntry = $"{operation} on {timestamp} - {operation} #{count + 1} by {userName} on {computerName}\r\n";
                }

                if (!File.Exists(_logFile))
                {
                    File.WriteAllText(_logFile, logHeader);
                }

                File.AppendAllText(_logFile, logEntry);

                if (IsModelOverrideActive)
                {
                    Log($"Log updated: {operation} #{count + 1} recorded for override FFU {Path.GetFileName(OverrideFFUFilePath)}");
                }
                else
                {
                    Log($"Log updated: {operation} #{count + 1} recorded.");
                }

                // Update the total deployment count if this was a deployment
                if (operation == "Deployment")
                {
                    TotalDeploymentCount = count + 1;
                }
            }
            catch (Exception ex)
            {
                Log($"Warning: Failed to update log: {ex.Message}", true);
            }
        }

        private int GetOperationCount(string operation = "Deployment")
        {
            if (!File.Exists(_logFile))
            {
                return 0;
            }

            try
            {
                string[] lines = File.ReadAllLines(_logFile);
                int count = 0;

                foreach (string line in lines)
                {
                    if (line.Contains($"{operation} on"))
                    {
                        count++;
                    }
                }
                return count;
            }
            catch
            {
                return 0;
            }
        }

        private void Cleanup()
        {
            // Stop the USB drive monitor timer
            if (_usbDriveMonitorTimer != null)
            {
                _usbDriveMonitorTimer.Stop();
                Log("USB drive monitoring stopped.");
            }

            if (File.Exists(_diskpartScript))
            {
                try { File.Delete(_diskpartScript); } catch { }
            }

            // Revoke all network connections to ensure credentials aren't persisted
            try
            {
                // First, try to disconnect from the specific server if we used credentials
                if (_useNetworkCredentials && IsNetworkAvailable)
                {
                    ExecuteProcess("net", $"use \\\\{_networkServer} /delete /y", false, false);
                }

                // Then, get a list of all network connections
                Process process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "net",
                        Arguments = "use",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                string output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                // Parse the output to find network connections to our server
                string[] lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string line in lines)
                {
                    if (line.Contains(_networkServer))
                    {
                        // Extract the connection path
                        string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length > 1)
                        {
                            string connectionPath = parts[1];
                            // Delete this connection
                            ExecuteProcess("net", $"use {connectionPath} /delete /y", false, false);
                            Log($"Revoked network access to: {connectionPath}", false);
                        }
                    }
                }

                // As a final step, clear any cached credentials from Windows Credential Manager
                ExecuteProcess("cmdkey", $"/delete:target={_networkServer}", false, false);

                Log("All network credentials have been revoked", false);
            }
            catch (Exception ex)
            {
                Log($"Warning: Could not fully revoke network credentials: {ex.Message}", true);
            }

            // Save the configuration to ensure any changes are persisted
            try
            {
                SaveConfig();
            }
            catch { }
        }

        private bool IsAdministrator()
        {
            var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            var principal = new System.Security.Principal.WindowsPrincipal(identity);
            return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
        }

        // Simple obfuscation methods for credentials
        private string ObfuscateCredential(string credential)
        {
            if (string.IsNullOrEmpty(credential))
                return string.Empty;

            // Convert to bytes
            byte[] bytes = System.Text.Encoding.UTF8.GetBytes(credential);

            // Simple XOR with a fixed key for basic obfuscation
            byte[] key = new byte[] { 0x43, 0x46, 0x47, 0x5F, 0x4F, 0x42, 0x46, 0x53 }; // "CFG_OBFS" in hex
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = (byte)(bytes[i] ^ key[i % key.Length]);
            }

            // Convert to Base64 for storage
            return Convert.ToBase64String(bytes);
        }

        private string DeobfuscateCredential(string obfuscatedCredential)
        {
            if (string.IsNullOrEmpty(obfuscatedCredential))
                return string.Empty;

            try
            {
                // Convert from Base64
                byte[] bytes = Convert.FromBase64String(obfuscatedCredential);

                // Apply the same XOR operation to decrypt
                byte[] key = new byte[] { 0x43, 0x46, 0x47, 0x5F, 0x4F, 0x42, 0x46, 0x53 }; // "CFG_OBFS" in hex
                for (int i = 0; i < bytes.Length; i++)
                {
                    bytes[i] = (byte)(bytes[i] ^ key[i % key.Length]);
                }

                // Convert back to string
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                // If decryption fails (e.g., not a valid Base64 string), return empty
                return string.Empty;
            }
        }

        // Track progress information
        private string _currentProgress = "";
        private string _progressLineId = "";

        public void Log(string message, bool isError = false)
        {
            // Check if this is a progress percentage line from DISM
            bool isProgressLine = message.Contains("%") && System.Text.RegularExpressions.Regex.IsMatch(message, "\\d+\\.?\\d*%");

            Application.Current.Dispatcher.Invoke(() =>
            {
                // Always process progress lines, but only process regular log messages if ShowLogging is enabled
                if (isProgressLine)
                {
                    // Extract just the percentage from the message
                    var match = System.Text.RegularExpressions.Regex.Match(message, "(\\d+\\.?\\d*)%");
                    if (match.Success)
                    {
                        string percentage = match.Groups[1].Value;

                        // If this is our first progress update, create a progress line
                        if (string.IsNullOrEmpty(_currentProgress))
                        {
                            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            _progressLineId = timestamp; // Use timestamp as a unique identifier
                            _currentProgress = $"[{timestamp}] Progress: {percentage}%\n";
                            LogContent += _currentProgress;
                        }
                        else
                        {
                            // For subsequent updates, replace the percentage in the existing line
                            string newProgressLine = $"[{_progressLineId}] Progress: {percentage}%\n";
                            LogContent = LogContent.Replace(_currentProgress, newProgressLine);
                            _currentProgress = newProgressLine;
                        }
                    }
                }
                else if (_showLogging) // Only log regular messages if ShowLogging is enabled
                {
                    // Regular log line
                    string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    string formattedMessage = $"[{timestamp}] {message}\n";

                    // If we were tracking progress, reset progress tracking
                    if (!string.IsNullOrEmpty(_currentProgress))
                    {
                        _currentProgress = "";
                        _progressLineId = "";
                    }

                    LogContent += formattedMessage;
                }
                else if (!_showLogging && !string.IsNullOrEmpty(_currentProgress))
                {
                    // If logging is disabled but we have a progress line and this is a non-progress message,
                    // we don't want to reset the progress tracking
                    // Do nothing - keep the progress line
                }

                OnLogUpdated();
            });
        }
        #endregion

        #region Command Methods
        private bool CanExecuteOperation()
        {
            // Can't execute any operation if another operation is in progress
            if (IsOperationInProgress)
                return false;

            // Can execute if either network is available or USB drive is being used and is available
            return IsNetworkAvailable || (UseUsbDrive && IsUsbDriveAvailable);
        }

        private bool CanExecuteAnyTime()
        {
            // These commands can be executed even during operations
            return !IsOperationInProgress;
        }

        private async void CaptureImage()
        {
            try
            {
                // Set operation in progress flag to disable buttons
                IsOperationInProgress = true;

                Log("Starting FFU image capture process...");

                // Check for administrator privileges
                if (!IsAdministrator())
                {
                    Log("Error: Administrator privileges required for FFU capture", true);
                    Log("Please restart the application as Administrator", true);
                    return;
                }

                // Check if we're using USB drive and if it's available
                if (UseUsbDrive)
                {
                    if (!IsUsbDriveAvailable)
                    {
                        Log("Error: Selected USB drive is not available", true);
                        Log("Please select a valid USB drive", true);
                        return;
                    }

                    // Verify the USB drive path exists
                    if (!Directory.Exists(_usbDrivePath))
                    {
                        Log($"Error: USB drive path not found: {_usbDrivePath}", true);
                        Log("Please select a valid USB drive", true);
                        return;
                    }
                }
                else if (!IsNetworkAvailable)
                {
                    Log("Error: Network is not available", true);
                    Log("Please check network connection or use USB drive", true);
                    return;
                }

                if (File.Exists(_ffuFile))
                {
                    Log($"Error: FFU image already exists for {PcModel} with this disk size", true);
                    Log("Please use a different model or remove existing FFU image", true);
                    return;
                }

                if (!Directory.Exists(_imagePath))
                {
                    try
                    {
                        Directory.CreateDirectory(_imagePath);
                    }
                    catch (Exception ex)
                    {
                        Log($"Error: Failed to create directory {_imagePath}", true);
                        Log(ex.Message, true);
                        return;
                    }
                }

                // Add a longer pause to ensure the user sees the warnings and can prepare the system
                await Task.Delay(5000);

                // Prepare the system for capture by flushing file system buffers
                Log("Preparing system for capture...");
                try
                {
                    // Create a temporary file and flush it to ensure disk is in a consistent state
                    string tempFilePath = "C:\\sync.bin";
                    using (FileStream fs = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                    {
                        fs.WriteByte(0); // Write a single byte to ensure the file is created
                        fs.Flush(); // Flush the stream to ensure data is written to disk
                    }
                    File.Delete(tempFilePath); // Delete the temporary file

                    // Give the system a moment to complete any pending I/O operations
                    Thread.Sleep(2000);
                }
                catch (Exception ex)
                {
                    Log($"Warning: System preparation step failed: {ex.Message}", true);
                    Log("Continuing with capture, but stability may be affected.", true);
                }

                Log("Capturing entire drive as FFU image...");

                Log("Starting FFU capture with progress tracking...");

                // Create cancellation token source for the operation
                using var cancellationTokenSource = new CancellationTokenSource();

                // Show progress dialog with FFU information
                string ffuFileName = Path.GetFileName(_ffuFile);
                string ffuLocation = Path.GetDirectoryName(_ffuFile) ?? "";

                var progressDialog = ShowProgressDialog("Capture",
                    $"Capturing PhysicalDrive{SelectedDiskNumber} to FFU image...",
                    cancellationTokenSource,
                    ffuFileName,
                    ffuLocation,
                    PcModel,
                    SelectedDiskSize);

                bool success = false;
                try
                {
                    // Use the real-time output method to show progress in the GUI
                    // Using /Compress:none for better stability during capture
                    success = await ExecuteProcessWithRealTimeOutput("dism",
                        $"/Capture-FFU /ImageFile:\"{_ffuFile}\" /CaptureDrive:\\\\.\\PhysicalDrive{SelectedDiskNumber} /Name:\"Full Drive\" /Description:\"Full drive image for {PcModel}\"",
                        true, false, progressDialog, cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    Log("Capture operation was cancelled by user", true);
                    success = false;
                }
                finally
                {
                    // Complete the progress dialog
                    progressDialog?.CompleteOperation(success, success ? "Capture completed successfully!" : "Capture operation failed or was cancelled.");
                }

                if (success)
                {
                    // Update the capture log after successful capture
                    UpdateLog("Capture");
                    Log($"FFU image capture completed: {_ffuFile}");

                    // Show post-capture dialog to create INI file (this already shows success with green checkmark)
                    await ShowPostCaptureDialog();
                }
                else
                {
                    Log("FFU image capture failed or was cancelled", true);

                    // Show failure notification for capture failures
                    ShowStatusNotification(false, "Capture", "Please check the log for error details.");
                }
            }
            finally
            {
                // Reset operation in progress flag to enable buttons
                IsOperationInProgress = false;
            }
        }

        private async void DeployImage()
        {
            try
            {
                // Set operation in progress flag to disable buttons
                IsOperationInProgress = true;

                Log("Starting FFU image deployment process...");

                // Check if we're using USB drive and if it's available
                if (UseUsbDrive)
                {
                    if (!IsUsbDriveAvailable)
                    {
                        Log("Error: Selected USB drive is not available", true);
                        Log("Please select a valid USB drive", true);
                        return;
                    }

                    // Verify the USB drive path exists
                    if (!Directory.Exists(_usbDrivePath))
                    {
                        Log($"Error: USB drive path not found: {_usbDrivePath}", true);
                        Log("Please select a valid USB drive", true);
                        return;
                    }
                }
                else if (!IsNetworkAvailable)
                {
                    Log("Error: Network is not available", true);
                    Log("Please check network connection or use USB drive", true);
                    return;
                }

                // Determine which FFU file to use
                string ffuFileToUse;
                if (IsModelOverrideActive && !string.IsNullOrEmpty(OverrideFFUFilePath))
                {
                    ffuFileToUse = OverrideFFUFilePath;
                    Log($"Using override FFU file: {OverrideModelName} from {ffuFileToUse}");
                }
                else
                {
                    ffuFileToUse = _ffuFile;
                    Log($"Using automatic model detection FFU file: {ffuFileToUse}");
                }

                if (!File.Exists(ffuFileToUse))
                {
                    Log($"Error: FFU image not found: {ffuFileToUse}", true);
                    if (IsModelOverrideActive)
                    {
                        Log("The selected override FFU file is not accessible. Please check the file path and try again.", true);
                    }
                    return;
                }

                // Add a brief pause to ensure the user sees the warning
                await Task.Delay(2000);

                // Create cancellation token source for the operation
                using var cancellationTokenSource = new CancellationTokenSource();

                // Show progress dialog with FFU information
                string ffuFileName = Path.GetFileName(ffuFileToUse);
                string ffuLocation = Path.GetDirectoryName(ffuFileToUse) ?? "";

                var progressDialog = ShowProgressDialog("Deployment",
                    $"Deploying FFU image to PhysicalDrive{SelectedDiskNumber}...",
                    cancellationTokenSource,
                    ffuFileName,
                    ffuLocation,
                    IsModelOverrideActive ? OverrideModelName : "Target Device",
                    SelectedDiskSize);

                bool success = false;
                try
                {
                    // Use the real-time output method to show progress in the GUI
                    success = await ExecuteProcessWithRealTimeOutput("dism",
                        $"/Apply-FFU /ImageFile:\"{ffuFileToUse}\" /ApplyDrive:\\\\.\\PhysicalDrive{SelectedDiskNumber}",
                        true, false, progressDialog, cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    Log("Deployment operation was cancelled by user", true);
                    success = false;
                }
                finally
                {
                    // Complete the progress dialog
                    progressDialog?.CompleteOperation(success, success ? "Deployment completed successfully!" : "Deployment operation failed or was cancelled.");
                }

                if (success)
                {
                    // Update the deployment log after successful deployment
                    UpdateLog("Deployment");

                    string successMessage;
                    if (IsModelOverrideActive)
                    {
                        Log($"FFU image deployment completed successfully: {OverrideModelName}");
                        successMessage = $"Override image deployed: {OverrideModelName}";
                    }
                    else
                    {
                        Log("FFU image deployment completed successfully");
                        successMessage = $"Image deployed to PhysicalDrive{SelectedDiskNumber}";
                    }

                    // Show success notification
                    ShowStatusNotification(true, "Deployment", successMessage);
                }
                else
                {
                    Log("FFU image deployment failed or was cancelled", true);

                    // Show failure notification
                    ShowStatusNotification(false, "Deployment", "Please check the log for error details.");
                }
            }
            finally
            {
                // Reset operation in progress flag to enable buttons
                IsOperationInProgress = false;
            }
        }

        private void ViewDeploymentHistory()
        {
            Log("Retrieving deployment history...");
            LogContent = ""; // Clear current log

            if (!File.Exists(_logFile))
            {
                Log("No deployment history available.");
                return;
            }

            try
            {
                string[] lines = File.ReadAllLines(_logFile);
                Log($"=== Deployment History for {PcModel} ===");
                foreach (string line in lines)
                {
                    Log(line);
                }
            }
            catch (Exception ex)
            {
                Log($"Error reading log file: {ex.Message}", true);
            }
        }

        private void ShowCredentialsDialog()
        {
            // Create and show the credentials dialog
            // We don't pass the current username for security reasons
            var dialog = new CredentialsDialog(_networkServer, string.Empty);

            // Set the owner to the main window
            dialog.Owner = Application.Current.MainWindow;

            // Show the dialog and wait for the result
            bool? result = dialog.ShowDialog();

            // If the user clicked Save, update the credentials
            if (result == true)
            {
                UpdateNetworkCredentials(dialog.Username, dialog.Password);
            }
        }

        private void ShowUsbDriveDialog()
        {
            // Create and show the USB drive selection dialog
            var dialog = new UsbDriveDialog(AvailableUsbDrives, _selectedUsbDrive);

            // Set the owner to the main window
            dialog.Owner = Application.Current.MainWindow;

            // Show the dialog and wait for the result
            bool? result = dialog.ShowDialog();

            // If the user clicked Save, update the USB drive settings
            if (result == true)
            {
                UpdateUsbDriveSettings(dialog.UseUsbDrive, dialog.SelectedDrive);
            }
        }

        public void RefreshUsbDrives()
        {
            Log("Refreshing available drives...");
            AvailableUsbDrives.Clear();
            bool previousDriveAvailability = IsUsbDriveAvailable;
            bool driveFound = false;

            try
            {
                // Get all available drives
                List<string> availableDrives = GetAvailableDrives();
                Log($"Found {availableDrives.Count} available drives");

                // Get all drives
                DriveInfo[] drives = DriveInfo.GetDrives();
                foreach (DriveInfo drive in drives)
                {
                    // Check if drive is ready to avoid exceptions
                    if (!drive.IsReady) continue;

                    // Skip the system drive (usually C:)
                    if (drive.Name.StartsWith(Environment.GetEnvironmentVariable("SystemDrive") ?? "C:", StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    try
                    {
                        string label = string.IsNullOrEmpty(drive.VolumeLabel) ? "No Label" : drive.VolumeLabel;
                        string driveName = $"{drive.Name} ({label}) - {FormatBytes(drive.TotalSize)} - {drive.DriveFormat}";
                        AvailableUsbDrives.Add(driveName);

                        Log($"Adding drive to UI list: {driveName} - Type: {drive.DriveType} - Format: {drive.DriveFormat}");

                        // If this is the previously selected drive, mark it as selected
                        if (_selectedUsbDrive == driveName)
                        {
                            _usbDrivePath = drive.Name.TrimEnd('\\');
                            IsUsbDriveAvailable = true;
                            driveFound = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log($"Error adding drive {drive.Name} to UI list: {ex.Message}");
                    }
                }

                // If we have USB drives but none is selected, select the first one
                if (AvailableUsbDrives.Count > 0 && string.IsNullOrEmpty(_selectedUsbDrive))
                {
                    _selectedUsbDrive = AvailableUsbDrives[0];
                    // Extract drive letter from the selected drive
                    string driveLetter = _selectedUsbDrive.Substring(0, 2);
                    _usbDrivePath = driveLetter;
                    IsUsbDriveAvailable = true;
                    driveFound = true;
                }
                else if (AvailableUsbDrives.Count == 0)
                {
                    IsUsbDriveAvailable = false;
                    Log("No drives found.", true);
                }
                else
                {
                    // Check if the previously selected drive is still available
                    if (!AvailableUsbDrives.Contains(_selectedUsbDrive))
                    {
                        IsUsbDriveAvailable = false;
                        Log("Previously selected drive is no longer available.", true);
                    }
                    else
                    {
                        driveFound = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"Error refreshing drives: {ex.Message}", true);
                IsUsbDriveAvailable = false;
            }

            // If we're using USB drive mode but the drive is not available, suggest network mode
            if (_useUsbDrive && !IsUsbDriveAvailable)
            {
                Log("Selected external drive is not available.", true);
                Log("You can use 'Check Network' button to connect to network mode if needed.", true);
            }
            // If the drive was previously unavailable but is now available, log this change
            else if (_useUsbDrive && IsUsbDriveAvailable && !previousDriveAvailability && driveFound)
            {
                Log($"External drive {_selectedUsbDrive} is now available.", true);

                // Update paths based on current mode
                InitializeVariables();

                // Re-read the INI file since the USB drive is now available
                ReadConfigIfExists();
            }
            else
            {
                // Update paths based on current mode
                InitializeVariables();
            }
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }
            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private List<string> GetAvailableDrives()
        {
            List<string> availableDrives = new List<string>();

            try
            {
                Log("Getting available drives...");

                // Get all drives
                DriveInfo[] allDrives = DriveInfo.GetDrives();
                foreach (DriveInfo drive in allDrives)
                {
                    if (!drive.IsReady) continue;

                    // Skip the system drive (usually C:)
                    if (drive.Name.StartsWith(Environment.GetEnvironmentVariable("SystemDrive") ?? "C:", StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    try
                    {
                        string label = string.IsNullOrEmpty(drive.VolumeLabel) ? "No Label" : drive.VolumeLabel;
                        string driveInfo = $"{drive.Name} ({label}) - {FormatBytes(drive.TotalSize)} - {drive.DriveFormat}";

                        availableDrives.Add(drive.Name);
                        Log($"Found drive: {driveInfo}");
                    }
                    catch (Exception ex)
                    {
                        Log($"Error getting drive info for {drive.Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"Error getting available drives: {ex.Message}", true);
            }

            Log($"Total drives found: {availableDrives.Count}");
            return availableDrives;
        }

        public void UpdateUsbDriveSettings(bool useUsbDrive, string selectedDrive)
        {
            UseUsbDrive = useUsbDrive;

            if (useUsbDrive && !string.IsNullOrEmpty(selectedDrive))
            {
                SelectedUsbDrive = selectedDrive;

                // Extract drive letter from the selected drive (format is "C: (Label) - Size - Format")
                string driveLetter = selectedDrive.Substring(0, 2);
                _usbDrivePath = driveLetter;

                // Check if the drive is available
                try
                {
                    DriveInfo drive = new DriveInfo(driveLetter);
                    IsUsbDriveAvailable = drive.IsReady;

                    if (drive.IsReady)
                    {
                        Log($"Using external drive: {selectedDrive}");
                        Log($"Drive format: {drive.DriveFormat}, Total size: {FormatBytes(drive.TotalSize)}, Free space: {FormatBytes(drive.AvailableFreeSpace)}");
                    }
                    else
                    {
                        Log($"Selected drive {driveLetter} is not ready.", true);

                        Log($"The selected drive {driveLetter} is not available.", true);
                        Log("Please connect the drive or use 'Check Network' button to switch to network mode.", true);
                    }
                }
                catch (Exception ex)
                {
                    Log($"Error checking drive {driveLetter}: {ex.Message}", true);
                    IsUsbDriveAvailable = false;

                    Log($"Error accessing drive {driveLetter}: {ex.Message}", true);
                    Log("Please connect a valid drive or use 'Check Network' button to switch to network mode.", true);
                }
            }
            else if (useUsbDrive)
            {
                Log("No drive selected.", true);
                IsUsbDriveAvailable = false;

                Log("No external drive selected.", true);
                Log("Please select a drive or use 'Check Network' button to switch to network mode.", true);
            }

            // Save the updated settings to the config file
            SaveConfig();

            // Reinitialize variables to update paths
            InitializeVariables();

            // Re-read the INI file with the new settings
            ReadConfigIfExists();
        }

        private void ShowSettingsDialog()
        {
            try
            {
                // Create and show the settings dialog
                var dialog = new SettingsDialog();

                // Set the DataContext to this view model so the commands work
                dialog.DataContext = this;

                // Set the owner to the main window
                dialog.Owner = Application.Current.MainWindow;

                // Show the dialog
                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                Log($"Error showing settings dialog: {ex.Message}", true);
            }
        }

        private void ShowModelOverrideDialog()
        {
            try
            {
                Log("Opening model override dialog...");

                // Create and show the model override dialog
                var dialog = new ModelOverrideDialog(this);

                // Set the owner to the main window
                dialog.Owner = Application.Current.MainWindow;

                Log("Model override dialog created successfully.");

                // Show the dialog and wait for the result
                bool? result = dialog.ShowDialog();

                // If the user selected an FFU file, update the override settings
                if (result == true && !string.IsNullOrEmpty(dialog.SelectedFFUPath))
                {
                    SetModelOverride(dialog.SelectedFFUPath, dialog.SelectedModelName);
                }
            }
            catch (Exception ex)
            {
                Log($"Error showing model override dialog: {ex.Message}", true);
                if (ex.InnerException != null)
                {
                    Log($"Inner exception: {ex.InnerException.Message}", true);
                }
                Log($"Stack trace: {ex.StackTrace}", true);
            }
        }

        public void SetModelOverride(string ffuFilePath, string modelName)
        {
            IsModelOverrideActive = true;
            OverrideFFUFilePath = ffuFilePath;
            OverrideModelName = modelName;

            Log($"Model override activated: Using {modelName} from {ffuFilePath}", true);
            Log("Note: Automatic model detection is now bypassed.", true);

            // Reinitialize variables to use override paths instead of model-based paths
            InitializeVariables();
            Log("Paths updated for override mode - no model-specific folders will be created.", true);
        }

        public void ClearModelOverride()
        {
            IsModelOverrideActive = false;
            OverrideFFUFilePath = string.Empty;
            OverrideModelName = string.Empty;

            Log("Model override cleared. Automatic model detection restored.", true);

            // Reinitialize variables to use normal model-based paths
            InitializeVariables();
            Log("Paths restored to automatic model detection mode.", true);
        }

        public string GetNetworkBasePath()
        {
            return _imageBasePath;
        }

        public string GetConfigFileLocation()
        {
            return _configFile;
        }

        public void ReloadConfig()
        {
            Console.WriteLine("Forcing config reload...");
            LoadConfig();
            InitializeVariables();
            Console.WriteLine("Config reload completed.");
        }

        private void RefreshUIAfterDiskChange()
        {
            try
            {
                Log("Refreshing UI after disk selection change...");

                // Re-initialize variables to update file paths with new disk size
                InitializeVariables();
                Log($"Variables re-initialized with disk size: {SelectedDiskSize}");

                // Re-read the INI file since the disk size (and thus file paths) may have changed
                ReadConfigIfExists();

                // Trigger property change notifications to refresh UI bindings
                OnPropertyChanged(nameof(SelectedDiskSize));
                OnPropertyChanged(nameof(CfgModel));
                OnPropertyChanged(nameof(OsType));

                Log("UI refresh completed after disk selection change.");
            }
            catch (Exception ex)
            {
                Log($"Error refreshing UI after disk change: {ex.Message}", true);
            }
        }

        private void ValidateAndUpdateSelectedDisk()
        {
            try
            {
                // Check if the currently selected disk number exists in the enumerated disks
                bool diskExists = false;
                foreach (string diskInfo in AvailableDisks)
                {
                    if (diskInfo.StartsWith($"Disk {SelectedDiskNumber}:"))
                    {
                        diskExists = true;
                        break;
                    }
                }

                if (!diskExists && AvailableDisks.Count > 0)
                {
                    // The configured disk doesn't exist, fall back to the first available disk
                    string firstDisk = AvailableDisks[0];
                    if (firstDisk.StartsWith("Disk "))
                    {
                        int endIndex = firstDisk.IndexOf(':', 5);
                        if (endIndex > 5)
                        {
                            string diskNumberStr = firstDisk.Substring(5, endIndex - 5);
                            if (int.TryParse(diskNumberStr, out int fallbackDiskNumber))
                            {
                                Log($"WARNING: Configured disk {SelectedDiskNumber} not found. Falling back to disk {fallbackDiskNumber}.", true);

                                // Set the backing field directly to avoid triggering UpdateSelectedDiskSize prematurely
                                _selectedDiskNumber = fallbackDiskNumber;

                                // Save the corrected disk number to config
                                SaveConfig();
                            }
                        }
                    }
                }

                // Now trigger property change notification and update the disk size
                OnPropertyChanged(nameof(SelectedDiskNumber));
                UpdateSelectedDiskSize();
            }
            catch (Exception ex)
            {
                Log($"Error validating selected disk: {ex.Message}", true);
                // Fallback to disk 0 if validation fails
                _selectedDiskNumber = 0;
                OnPropertyChanged(nameof(SelectedDiskNumber));
                UpdateSelectedDiskSize();
            }
        }

        public List<DriveInfo> GetAvailableUsbDriveInfos()
        {
            var usbDrives = new List<DriveInfo>();

            try
            {
                DriveInfo[] drives = DriveInfo.GetDrives();
                foreach (DriveInfo drive in drives)
                {
                    if (drive.IsReady && (drive.DriveType == DriveType.Removable || drive.DriveType == DriveType.Fixed))
                    {
                        // Skip system drives (typically C:)
                        if (drive.Name.ToUpper() != "C:\\")
                        {
                            usbDrives.Add(drive);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"Error getting USB drive information: {ex.Message}");
            }

            return usbDrives;
        }

        private void ExitApplication()
        {
            // Log that we're exiting and revoking network access
            Log("Exiting application and revoking all network access...");

            // Perform cleanup which includes revoking network access
            Cleanup();

            // Shutdown the application
            Application.Current.Shutdown();
        }

        // Method to explicitly revoke network access
        public void RevokeNetworkAccess()
        {
            try
            {
                Log("Revoking network access...");

                // Disconnect from the network share
                if (IsNetworkAvailable)
                {
                    // First try to disconnect from the specific server
                    ExecuteProcess("net", $"use \\\\{_networkServer} /delete /y", true, false);

                    // Then clear any cached credentials
                    ExecuteProcess("cmdkey", $"/delete:target={_networkServer}", true, false);

                    // Update the network status
                    IsNetworkAvailable = false;

                    Log("Network access successfully revoked");
                }
                else
                {
                    Log("No active network connections to revoke");
                }
            }
            catch (Exception ex)
            {
                Log($"Error revoking network access: {ex.Message}", true);
            }
        }
        private void ShowDiskSelectionDialog()
        {
            try
            {
                // Refresh the list of available disks
                RefreshAvailableDisks();

                // Ensure we have at least one disk to show
                if (AvailableDisks.Count == 0)
                {
                    for (int i = 0; i < 4; i++)
                    {
                        AvailableDisks.Add($"Disk {i}: PhysicalDrive{i}");
                    }
                    Log("No disks were found. Using default disk list.");
                }

                // Create the disk selection dialog and pass the selected disk number
                var dialog = new DiskSelectionDialog(AvailableDisks, SelectedDiskNumber); // Ensure this matches the expected constructor

                // Set the owner to the main window
                dialog.Owner = Application.Current.MainWindow;

                // Show the dialog and wait for the result
                bool? result = dialog.ShowDialog();

                // If OK is clicked, update the selected disk number
                if (result == true)
                {
                    SelectedDiskNumber = dialog.SelectedDiskNumber;  // Ensure SelectedDiskNumber is bound correctly for UI updates

                    // Save the updated settings to the config file
                    SaveConfig();

                    Log($"Selected disk changed to: PhysicalDrive{SelectedDiskNumber}");

                    // Refresh UI and re-read INI file with new disk size
                    RefreshUIAfterDiskChange();
                }
            }
            catch (Exception ex)
            {
                Log($"Error showing disk selection dialog: {ex.Message}", true);
                if (ex.InnerException != null)
                {
                    Log($"Inner exception: {ex.InnerException.Message}", true);
                }
            }
        }

        public void RefreshAvailableDisks()
        {
            Log("Refreshing available physical disks...");
            AvailableDisks.Clear();

            try
            {
                // Use PowerShell to get disk information with enhanced detection for eMMC and other storage types
                Process process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powershell",
                        Arguments = "-NoProfile -Command \"Get-Disk | Select-Object Number, FriendlyName, Size, PartitionStyle, BusType, MediaType | Sort-Object Number | ConvertTo-Json\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                string output = process.StandardOutput.ReadToEnd().Trim();
                string errorOutput = process.StandardError.ReadToEnd().Trim();
                process.WaitForExit();

                if (!string.IsNullOrEmpty(errorOutput))
                {
                    Log($"PowerShell error during disk enumeration: {errorOutput}");
                }

                if (!string.IsNullOrEmpty(output))
                {
                    // Parse the JSON output
                    try
                    {
                        // Check if the output is for a single disk (not in an array)
                        if (output.TrimStart().StartsWith("{"))
                        {
                            // Single disk object
                            var disk = System.Text.Json.JsonSerializer.Deserialize<DiskInfo>(output);
                            if (disk != null)
                            {
                                string diskSize = FormatBytes(disk.Size);
                                string storageType = GetStorageTypeDescription(disk.BusType, disk.MediaType, disk.FriendlyName);
                                string diskInfo = $"Disk {disk.Number}: {disk.FriendlyName} - {diskSize} - {storageType}";
                                AvailableDisks.Add(diskInfo);
                                Log($"Found disk: {diskInfo}");
                            }
                        }
                        else
                        {
                            // Multiple disks in an array
                            var disks = System.Text.Json.JsonSerializer.Deserialize<List<DiskInfo>>(output);
                            if (disks != null)
                            {
                                foreach (var disk in disks)
                                {
                                    string diskSize = FormatBytes(disk.Size);
                                    string storageType = GetStorageTypeDescription(disk.BusType, disk.MediaType, disk.FriendlyName);
                                    string diskInfo = $"Disk {disk.Number}: {disk.FriendlyName} - {diskSize} - {storageType}";
                                    AvailableDisks.Add(diskInfo);
                                    Log($"Found disk: {diskInfo}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log($"Error parsing disk information: {ex.Message}", true);
                        Log($"Raw output: {output.Substring(0, Math.Min(100, output.Length))}...", true);

                        // Try alternative method using WMIC for better eMMC detection
                        if (TryEnumerateDisksWithWMIC())
                        {
                            Log("Successfully enumerated disks using WMIC fallback method.");
                        }
                        else
                        {
                            // Final fallback to simple disk enumeration
                            Log("Using simple disk enumeration as final fallback.");
                            for (int i = 0; i < 4; i++) // Enumerate disks 0-3
                            {
                                AvailableDisks.Add($"Disk {i}: PhysicalDrive{i}");
                            }
                        }
                    }
                }
                else
                {
                    // Try alternative method using WMIC for better eMMC detection
                    if (TryEnumerateDisksWithWMIC())
                    {
                        Log("Successfully enumerated disks using WMIC fallback method.");
                    }
                    else
                    {
                        // Final fallback to simple disk enumeration
                        Log("Using simple disk enumeration as final fallback.");
                        for (int i = 0; i < 4; i++) // Enumerate disks 0-3
                        {
                            AvailableDisks.Add($"Disk {i}: PhysicalDrive{i}");
                        }
                    }
                }

                Log($"Found {AvailableDisks.Count} physical disks");
            }
            catch (Exception ex)
            {
                Log($"Error refreshing disks: {ex.Message}", true);

                // Try alternative method using WMIC for better eMMC detection
                if (TryEnumerateDisksWithWMIC())
                {
                    Log("Successfully enumerated disks using WMIC fallback method.");
                }
                else
                {
                    // Final fallback to simple disk enumeration
                    Log("Using simple disk enumeration as final fallback.");
                    for (int i = 0; i < 4; i++) // Enumerate disks 0-3
                    {
                        AvailableDisks.Add($"Disk {i}: PhysicalDrive{i}");
                    }
                }
            }

            // Update the selected disk size after refreshing
            UpdateSelectedDiskSize();
        }

        private void UpdateSelectedDiskSize()
        {
            try
            {
                // Use PowerShell to get the size of the selected disk (consistent with RefreshAvailableDisks)
                Process process = new()
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powershell",
                        Arguments = $"-NoProfile -Command \"Get-Disk | Where-Object Number -eq {SelectedDiskNumber} | Select-Object Size | ConvertTo-Json\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                string output = process.StandardOutput.ReadToEnd().Trim();
                string errorOutput = process.StandardError.ReadToEnd().Trim();
                process.WaitForExit();

                if (!string.IsNullOrEmpty(errorOutput))
                {
                    Log($"PowerShell error getting disk size: {errorOutput}");
                }

                if (!string.IsNullOrEmpty(output) && output != "null")
                {
                    try
                    {
                        // Parse the JSON output
                        var diskSizeInfo = System.Text.Json.JsonSerializer.Deserialize<DiskSizeInfo>(output);
                        if (diskSizeInfo != null && diskSizeInfo.Size > 0)
                        {
                            // Convert to GB
                            double sizeInGB = diskSizeInfo.Size / (1024.0 * 1024.0 * 1024.0);

                            // Round to nearest standard SSD size
                            int roundedSize = RoundToStandardSSDSize(sizeInGB);
                            string newDiskSize = $"Size: {roundedSize}GB";

                            // Only update if the size has changed
                            if (SelectedDiskSize != newDiskSize)
                            {
                                SelectedDiskSize = newDiskSize;
                                Log($"Updated disk size for PhysicalDrive{SelectedDiskNumber}: {newDiskSize}");
                                // Reinitialize variables to update file paths with new disk size
                                InitializeVariables();
                            }
                        }
                        else
                        {
                            Log($"No size information found for PhysicalDrive{SelectedDiskNumber}");
                            SelectedDiskSize = "Size: Unknown";
                        }
                    }
                    catch (Exception ex)
                    {
                        Log($"Error parsing disk size JSON: {ex.Message}");
                        Log($"Raw output: {output}");
                        SelectedDiskSize = "Size: Unknown";
                    }
                }
                else
                {
                    Log($"No disk found with number {SelectedDiskNumber}");
                    SelectedDiskSize = "Size: Unknown";
                }
            }
            catch (Exception ex)
            {
                Log($"Warning: Could not get disk size for PhysicalDrive{SelectedDiskNumber}: {ex.Message}");
                SelectedDiskSize = "Size: Unknown";
            }
        }

        private int RoundToStandardSSDSize(double sizeInGB)
        {
            // Common SSD sizes in GB
            int[] standardSizes = [16, 32, 64, 128, 256, 512, 1024, 2048, 4096];

            // Find the closest standard size
            int closestSize = standardSizes[0];
            double minDifference = Math.Abs(sizeInGB - standardSizes[0]);

            foreach (int size in standardSizes)
            {
                double difference = Math.Abs(sizeInGB - size);
                if (difference < minDifference)
                {
                    minDifference = difference;
                    closestSize = size;
                }
            }

            return closestSize;
        }

        private void CreateINIFile(string osType, string platformName, string recoveryPackID)
        {
            try
            {
                // Get disk size in GB from SelectedDiskSize
                string diskSizeGB = "Unknown";
                if (SelectedDiskSize.StartsWith("Size: "))
                {
                    diskSizeGB = SelectedDiskSize.Substring(6).Replace("GB", "").Trim();
                }

                // Create the INI file content in the format: MODELNAME;OS Type - Storage Size - Platform RecoveryPackID
                string iniContent = $"{PcModel};{osType} - {diskSizeGB}GB - {platformName} {recoveryPackID}";

                // Ensure the directory exists
                string? directory = Path.GetDirectoryName(_iniFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Log($"Created directory: {directory}");
                }

                // Write the INI file
                File.WriteAllText(_iniFile, iniContent);

                Log($"INI file created successfully: {_iniFile}");
                Log($"INI content: {iniContent}");

                // Update the UI properties to reflect the new INI file
                CfgModel = PcModel;
                OsType = $"{osType} - {diskSizeGB}GB - {platformName} {recoveryPackID}";

                Log("UI updated with new INI file information.");
            }
            catch (Exception ex)
            {
                Log($"Error creating INI file: {ex.Message}", true);
            }
        }

        private void ShowStatusNotification(bool success, string operationType, string? additionalMessage = null)
        {
            try
            {
                // Show the notification on the UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var notification = new StatusNotificationPopup()
                    {
                        Owner = Application.Current.MainWindow
                    };
                    notification.ShowNotification(success, operationType, additionalMessage);
                });

                Log($"Status notification shown: {operationType} {(success ? "successful" : "failed")}");
            }
            catch (Exception ex)
            {
                Log($"Error showing status notification: {ex.Message}", true);

                // Fallback to log message
                string status = success ? "completed successfully" : "failed";
                Log($"{operationType} {status}", !success);
            }
        }

        private ProgressDialog? ShowProgressDialog(string operationType, string description, CancellationTokenSource cancellationTokenSource, string? ffuFileName = null, string? ffuLocation = null, string? pcModel = null, string? storageSize = null)
        {
            try
            {
                ProgressDialog? progressDialog = null;

                Application.Current.Dispatcher.Invoke(() =>
                {
                    progressDialog = new ProgressDialog()
                    {
                        Owner = Application.Current.MainWindow
                    };

                    progressDialog.StartOperation(operationType, description, cancellationTokenSource, ffuFileName, ffuLocation, pcModel, storageSize);
                    progressDialog.Show();
                });

                return progressDialog;
            }
            catch (Exception ex)
            {
                Log($"Error showing progress dialog: {ex.Message}", true);
                return null;
            }
        }

        private long GetDiskSizeInBytes(int diskNumber)
        {
            try
            {
                // Parse the disk size from the SelectedDiskSize property
                // Format is "Size: XXX GB" or "Size: XXX.X GB"
                if (!string.IsNullOrEmpty(SelectedDiskSize))
                {
                    Log($"Parsing disk size from: '{SelectedDiskSize}'");

                    // Remove "Size:" prefix and clean up the text
                    var sizeText = SelectedDiskSize.Replace("Size:", "").Replace("GB", "").Replace("TB", "").Trim();

                    if (double.TryParse(sizeText, out double sizeValue))
                    {
                        long bytes;
                        // Convert to bytes
                        if (SelectedDiskSize.Contains("TB"))
                        {
                            bytes = (long)(sizeValue * 1024 * 1024 * 1024 * 1024);
                        }
                        else
                        {
                            bytes = (long)(sizeValue * 1024 * 1024 * 1024);
                        }

                        Log($"Calculated disk size: {sizeValue} GB = {bytes:N0} bytes");
                        return bytes;
                    }
                    else
                    {
                        Log($"Failed to parse size value from: '{sizeText}'");
                    }
                }
                else
                {
                    Log("SelectedDiskSize is empty or null");
                }

                // Fallback: Use a more reasonable default based on common USB sizes
                Log("Using fallback disk size: 32 GB");
                return 32L * 1024 * 1024 * 1024; // 32 GB default
            }
            catch (Exception ex)
            {
                Log($"Error calculating disk size: {ex.Message}", true);
                return 32L * 1024 * 1024 * 1024; // 32 GB default
            }
        }

        private long GetFFUFileSizeInBytes(string ffuFilePath)
        {
            try
            {
                if (File.Exists(ffuFilePath))
                {
                    var fileInfo = new FileInfo(ffuFilePath);
                    Log($"FFU file size: {FormatBytes(fileInfo.Length)}");
                    return fileInfo.Length;
                }

                // Fallback: return a default size (10 GB)
                Log("FFU file not found, using fallback size: 10 GB");
                return 10L * 1024 * 1024 * 1024;
            }
            catch (Exception ex)
            {
                Log($"Error getting FFU file size: {ex.Message}", true);
                return 10L * 1024 * 1024 * 1024; // Default 10 GB
            }
        }

        private long EstimateCaptureSize(long diskCapacityBytes)
        {
            // DISM captures compressed used space, which is typically much smaller than disk capacity
            // For progress estimation, we'll use a conservative estimate based on common scenarios:

            if (diskCapacityBytes <= 32L * 1024 * 1024 * 1024) // <= 32 GB (USB drives)
            {
                // Small drives are often 20-50% full, compressed to ~10-30% of capacity
                return diskCapacityBytes / 4; // 25% of capacity
            }
            else if (diskCapacityBytes <= 256L * 1024 * 1024 * 1024) // <= 256 GB (SSDs)
            {
                // Medium drives are often 30-70% full, compressed to ~15-35% of capacity
                return diskCapacityBytes / 3; // 33% of capacity
            }
            else // > 256 GB (Large drives)
            {
                // Large drives are often 50-80% full, compressed to ~20-40% of capacity
                return diskCapacityBytes / 2; // 50% of capacity
            }
        }



        private async Task ShowPostCaptureDialog()
        {
            try
            {
                // Get disk size for display
                string diskSizeDisplay = SelectedDiskSize.StartsWith("Size: ") ?
                    SelectedDiskSize.Substring(6) : SelectedDiskSize;

                // Show the dialog on the UI thread
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var dialog = new PostCaptureDialog(PcModel, diskSizeDisplay)
                    {
                        Owner = Application.Current.MainWindow
                    };

                    bool? result = dialog.ShowDialog();

                    if (result == true && dialog.ShouldCreateINI)
                    {
                        // Create the INI file with the provided information
                        CreateINIFile(dialog.SelectedOSType, dialog.PlatformName, dialog.RecoveryPackID);
                        Log("Post-capture INI file creation completed.");
                    }
                    else
                    {
                        Log("INI file creation skipped by user.");
                    }
                });
            }
            catch (Exception ex)
            {
                Log($"Error showing post-capture dialog: {ex.Message}", true);
            }
        }

        private string GetStorageTypeDescription(string busType, string mediaType, string friendlyName)
        {
            // Prioritize showing the most relevant storage type information

            // Check for eMMC in the friendly name first (most specific)
            if (!string.IsNullOrEmpty(friendlyName) &&
                (friendlyName.ToLower().Contains("emmc") || friendlyName.ToLower().Contains("mmc")))
            {
                return "eMMC";
            }

            // Check for USB connection
            if (!string.IsNullOrEmpty(busType) && busType.Equals("USB", StringComparison.OrdinalIgnoreCase))
            {
                return "USB";
            }

            // Check for specific media types
            if (!string.IsNullOrEmpty(mediaType))
            {
                if (mediaType.Equals("SSD", StringComparison.OrdinalIgnoreCase))
                    return "SSD";
                if (mediaType.Equals("HDD", StringComparison.OrdinalIgnoreCase))
                    return "HDD";
            }

            // Check friendly name for SSD/HDD indicators
            if (!string.IsNullOrEmpty(friendlyName))
            {
                string lowerName = friendlyName.ToLower();
                if (lowerName.Contains("ssd") || lowerName.Contains("solid state"))
                    return "SSD";
                if (lowerName.Contains("hdd") || lowerName.Contains("hard disk"))
                    return "HDD";
            }

            // Check bus type for other specific types
            if (!string.IsNullOrEmpty(busType))
            {
                if (busType.Equals("NVMe", StringComparison.OrdinalIgnoreCase))
                    return "NVMe SSD";
                if (busType.Equals("SATA", StringComparison.OrdinalIgnoreCase))
                    return "SATA";
                if (busType.Equals("IDE", StringComparison.OrdinalIgnoreCase))
                    return "IDE";
            }

            // Default fallback
            return "Storage";
        }

        private bool TryEnumerateDisksWithWMIC()
        {
            try
            {
                Log("Attempting disk enumeration using WMIC...");

                // Use WMIC to get disk information
                Process process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "wmic",
                        Arguments = "diskdrive get Index,Model,Size,InterfaceType /format:csv",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                string output = process.StandardOutput.ReadToEnd().Trim();
                string errorOutput = process.StandardError.ReadToEnd().Trim();
                process.WaitForExit();

                if (!string.IsNullOrEmpty(errorOutput))
                {
                    Log($"WMIC error: {errorOutput}");
                }

                if (!string.IsNullOrEmpty(output))
                {
                    string[] lines = output.Split('\n');
                    bool foundDisks = false;

                    foreach (string line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line) || line.StartsWith("Node,"))
                            continue;

                        string[] parts = line.Split(',');
                        if (parts.Length >= 4)
                        {
                            // Try to parse: Node,Index,InterfaceType,Model,Size
                            if (int.TryParse(parts[1]?.Trim(), out int diskIndex) &&
                                long.TryParse(parts[4]?.Trim(), out long size) &&
                                size > 0)
                            {
                                string model = parts[3]?.Trim() ?? "Unknown";
                                string interfaceType = parts[2]?.Trim() ?? "Unknown";
                                string diskSize = FormatBytes(size);

                                string diskInfo = $"Disk {diskIndex}: {model} - {diskSize} - {interfaceType}";
                                AvailableDisks.Add(diskInfo);
                                Log($"Found disk via WMIC: {diskInfo}");
                                foundDisks = true;
                            }
                        }
                    }

                    return foundDisks;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log($"Error in WMIC disk enumeration: {ex.Message}");
                return false;
            }
        }
        #endregion

        // Class to deserialize disk information from PowerShell
        private class DiskInfo
        {
            public int Number { get; set; }
            public string FriendlyName { get; set; } = string.Empty;
            public long Size { get; set; }
            public string PartitionStyle { get; set; } = string.Empty;
            public string BusType { get; set; } = string.Empty;
            public string MediaType { get; set; } = string.Empty;
        }

        // Class to deserialize disk size information from PowerShell
        private class DiskSizeInfo
        {
            public long Size { get; set; }
        }

        // FormatBytes method is already defined elsewhere in the class

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler? PropertyChanged;
        public event EventHandler? LogUpdated;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }

        protected virtual void OnLogUpdated()
        {
            LogUpdated?.Invoke(this, EventArgs.Empty);
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }
}