using System;
using System.Windows;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for PostCaptureDialog.xaml
    /// </summary>
    public partial class PostCaptureDialog : Window
    {
        public string SelectedOSType { get; private set; } = string.Empty;
        public string PlatformName { get; private set; } = string.Empty;
        public string RecoveryPackID { get; private set; } = string.Empty;
        public bool ShouldCreateINI { get; private set; } = false;

        public PostCaptureDialog(string detectedModel, string diskSize)
        {
            InitializeComponent();
            
            // Set the detected model (read-only)
            DetectedModelTextBox.Text = $"{detectedModel} - {diskSize}";
            
            // Set default OS selection to Windows 11 Home
            OSTypeComboBox.SelectedIndex = 3; // Windows 11 Home
            
            // Focus on the platform name textbox
            PlatformNameTextBox.Focus();
            PlatformNameTextBox.SelectAll();
        }

        private void CreateINIButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate inputs
            if (OSTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("Please select an Operating System type.", "Missing Information", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                OSTypeComboBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(PlatformNameTextBox.Text))
            {
                MessageBox.Show("Please enter a Platform Name.", "Missing Information", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                PlatformNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(RecoveryPackTextBox.Text))
            {
                MessageBox.Show("Please enter a Recovery Pack ID.", "Missing Information", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                RecoveryPackTextBox.Focus();
                return;
            }

            // Store the values
            SelectedOSType = ((System.Windows.Controls.ComboBoxItem)OSTypeComboBox.SelectedItem).Content.ToString() ?? string.Empty;
            PlatformName = PlatformNameTextBox.Text.Trim();
            RecoveryPackID = RecoveryPackTextBox.Text.Trim();
            ShouldCreateINI = true;

            DialogResult = true;
            Close();
        }

        private void SkipButton_Click(object sender, RoutedEventArgs e)
        {
            ShouldCreateINI = false;
            DialogResult = false;
            Close();
        }
    }
}
