{"version": 3, "targets": {"net9.0-windows7.0": {"Costura.Fody/6.0.0": {"type": "package", "dependencies": {"Fody": "6.8.2"}, "compile": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "build": {"build/Costura.Fody.props": {}, "build/Costura.Fody.targets": {}}}, "Fody/6.8.2": {"type": "package", "build": {"build/Fody.targets": {}}}, "Microsoft.NET.ILLink.Tasks/9.0.8": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}, "net9.0-windows7.0/win-x64": {"Costura.Fody/6.0.0": {"type": "package", "dependencies": {"Fody": "6.8.2"}, "compile": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Costura.dll": {"related": ".pdb;.xml"}}, "build": {"build/Costura.Fody.props": {}, "build/Costura.Fody.targets": {}}}, "Fody/6.8.2": {"type": "package", "build": {"build/Fody.targets": {}}}, "Microsoft.NET.ILLink.Tasks/9.0.8": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}}}, "libraries": {"Costura.Fody/6.0.0": {"sha512": "3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "type": "package", "path": "costura.fody/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Costura.Fody.props", "build/Costura.Fody.targets", "costura.fody.6.0.0.nupkg.sha512", "costura.fody.nuspec", "icon.png", "lib/netstandard2.0/Costura.dll", "lib/netstandard2.0/Costura.pdb", "lib/netstandard2.0/Costura.xml", "netclassicweaver/Costura.Fody.dll", "netclassicweaver/Costura.Fody.xcf", "netstandardweaver/Costura.Fody.dll", "netstandardweaver/Costura.Fody.xcf"]}, "Fody/6.8.2": {"sha512": "sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "type": "package", "path": "fody/6.8.2", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/Fody.targets", "fody.6.8.2.nupkg.sha512", "fody.nuspec", "netclassictask/Fody.dll", "netclassictask/FodyCommon.dll", "netclassictask/FodyHelpers.dll", "netclassictask/FodyIsolated.dll", "netclassictask/Mono.Cecil.Pdb.dll", "netclassictask/Mono.Cecil.Pdb.pdb", "netclassictask/Mono.Cecil.Rocks.dll", "netclassictask/Mono.Cecil.Rocks.pdb", "netclassictask/Mono.Cecil.dll", "netclassictask/Mono.Cecil.pdb", "netstandardtask/Fody.dll", "netstandardtask/FodyCommon.dll", "netstandardtask/FodyHelpers.dll", "netstandardtask/FodyIsolated.dll", "netstandardtask/Mono.Cecil.Pdb.dll", "netstandardtask/Mono.Cecil.Pdb.pdb", "netstandardtask/Mono.Cecil.Rocks.dll", "netstandardtask/Mono.Cecil.Rocks.pdb", "netstandardtask/Mono.Cecil.dll", "netstandardtask/Mono.Cecil.pdb", "readme.md"]}, "Microsoft.NET.ILLink.Tasks/9.0.8": {"sha512": "rd1CbIsMtVPtZNTIVD6Xydue//klYOOQIDpRgu3BHtv17AlpRs74/6QFbcYgMm/jL+naVU2T3OFLxVSLV5lQLQ==", "type": "package", "path": "microsoft.net.illink.tasks/9.0.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.9.0.8.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net9.0/ILLink.Tasks.deps.json", "tools/net9.0/ILLink.Tasks.dll", "tools/net9.0/Mono.Cecil.Mdb.dll", "tools/net9.0/Mono.Cecil.Pdb.dll", "tools/net9.0/Mono.Cecil.Rocks.dll", "tools/net9.0/Mono.Cecil.dll", "tools/net9.0/Sdk/Sdk.props", "tools/net9.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net9.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net9.0/build/Microsoft.NET.ILLink.targets", "tools/net9.0/illink.deps.json", "tools/net9.0/illink.dll", "tools/net9.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Costura.Fody >= 6.0.0", "Microsoft.NET.ILLink.Tasks >= 9.0.8", "System.Management >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "0.5.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj", "projectName": "FFUCaptureDeploymentTool", "projectPath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Costura.Fody": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.8, )", "autoReferenced": true}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.8, 9.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}