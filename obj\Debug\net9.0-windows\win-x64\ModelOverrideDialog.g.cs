﻿#pragma checksum "..\..\..\..\ModelOverrideDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "89A20130D5BD731E2A64852534083F891F2C45E5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FFUCaptureDeploymentTool;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FFUCaptureDeploymentTool {
    
    
    /// <summary>
    /// ModelOverrideDialog
    /// </summary>
    public partial class ModelOverrideDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 81 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton NetworkRadioButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton UsbRadioButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox FFUFilesListBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedFileInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\ModelOverrideDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FFUCaptureDeploymentTool;V0.5.0.0;component/modeloverridedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ModelOverrideDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NetworkRadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 83 "..\..\..\..\ModelOverrideDialog.xaml"
            this.NetworkRadioButton.Checked += new System.Windows.RoutedEventHandler(this.LocationRadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.UsbRadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 86 "..\..\..\..\ModelOverrideDialog.xaml"
            this.UsbRadioButton.Checked += new System.Windows.RoutedEventHandler(this.LocationRadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 110 "..\..\..\..\ModelOverrideDialog.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 117 "..\..\..\..\ModelOverrideDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FFUFilesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 123 "..\..\..\..\ModelOverrideDialog.xaml"
            this.FFUFilesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FFUFilesListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SelectedFileInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 147 "..\..\..\..\ModelOverrideDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearOverrideButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 149 "..\..\..\..\ModelOverrideDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SelectButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\ModelOverrideDialog.xaml"
            this.SelectButton.Click += new System.Windows.RoutedEventHandler(this.SelectButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

