﻿using System;
using System.Windows;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Create splash window
            var splash = new SplashWindow();
            splash.SetSubtitle("Initializing...");
            splash.UpdateProgress(0, "Starting up");
            splash.Show();

            // Prepare ViewModel and hook progress
            var vm = new FFUViewModel();
            vm.InitializationProgressChanged += (percent, msg) =>
            {
                splash.UpdateProgress(percent, msg);
            };

            // Perform initialization without blocking UI thread
            // But FFUViewModel.Initialize is currently synchronous; run it in dispatcher idle
            splash.Dispatcher.InvokeAsync(() =>
            {
                vm.Initialize();

                // After init, show main window
                var main = new MainWindow(vm);
                main.Show();

                // Close splash
                splash.Close();
            });
        }
    }
}