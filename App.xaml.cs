using System;
using System.Windows;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected async override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Create splash window
            var splash = new SplashWindow();
            splash.SetSubtitle("Initializing...");
            splash.UpdateProgress(0, "Starting up");
            splash.Show();

            // Prepare ViewModel and hook progress
            var vm = new FFUViewModel();
            vm.InitializationProgressChanged += (percent, msg) =>
            {
                splash.UpdateProgress(percent, msg);
            };

            // Run initialization on a background thread so UI can update
            await System.Threading.Tasks.Task.Run(() => vm.Initialize());

            // After init, show main window
            var main = new MainWindow(vm);
            main.Show();

            // Close splash
            splash.Close();
        }
    }
}