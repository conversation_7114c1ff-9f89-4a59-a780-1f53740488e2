using System.Windows;

namespace FFUCaptureDeploymentTool
{
    public partial class CredentialsDialog : Window
    {
        public string Username { get; private set; } = string.Empty;
        public string Password { get; private set; } = string.Empty;

        public CredentialsDialog(string server, string currentUsername)
        {
            InitializeComponent();

            // Set the server text box
            ServerTextBox.Text = server;

            // Always start with empty fields for security
            UsernameTextBox.Text = string.Empty;

            // Always focus on the username field first
            UsernameTextBox.Focus();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // Get the values from the text boxes
            Username = UsernameTextBox.Text;
            Password = PasswordBox.Password;

            // Set the dialog result to true (OK)
            DialogResult = true;

            // Close the dialog
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // Set the dialog result to false (Cancel)
            DialogResult = false;

            // Close the dialog
            Close();
        }
    }
}
