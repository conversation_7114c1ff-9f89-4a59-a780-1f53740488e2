using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for StatusNotificationPopup.xaml
    /// </summary>
    public partial class StatusNotificationPopup : Window
    {
        public StatusNotificationPopup()
        {
            InitializeComponent();
        }

        public void ShowNotification(bool success, string operationType, string? additionalMessage = null)
        {
            try
            {
                // Set up the notification content
                if (success)
                {
                    SetupSuccessNotification(operationType, additionalMessage);
                }
                else
                {
                    SetupFailureNotification(operationType, additionalMessage);
                }

                // Show as modal dialog
                this.ShowDialog();
            }
            catch (Exception)
            {
                // Fallback to simple message box if dialog fails
                MessageBox.Show($"{operationType} {(success ? "completed successfully" : "failed")}",
                               "Operation Status", MessageBoxButton.OK,
                               success ? MessageBoxImage.Information : MessageBoxImage.Warning);
            }
        }

        private void SetupSuccessNotification(string operationType, string? additionalMessage)
        {
            // Set window title
            this.Title = $"{operationType} Successful";

            // Set icon from embedded resource
            try
            {
                var uri = new Uri("pack://application:,,,/svg/check.svg");
                StatusIcon.Source = new BitmapImage(uri);
            }
            catch
            {
                // If resource loading fails, hide the icon
                StatusIcon.Visibility = Visibility.Collapsed;
            }

            // Set text and styling
            StatusTitle.Text = $"{operationType} Successful!";
            StatusTitle.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green

            StatusMessage.Text = $"The {operationType.ToLower()} operation completed successfully.";

            if (!string.IsNullOrEmpty(additionalMessage))
            {
                StatusDetails.Text = additionalMessage;
                StatusDetails.Visibility = Visibility.Visible;
            }
            else
            {
                StatusDetails.Visibility = Visibility.Collapsed;
            }
        }

        private void SetupFailureNotification(string operationType, string? additionalMessage)
        {
            // Set window title
            this.Title = $"{operationType} Failed";

            // Set icon from embedded resource
            try
            {
                var uri = new Uri("pack://application:,,,/svg/warning.svg");
                StatusIcon.Source = new BitmapImage(uri);
            }
            catch
            {
                // If resource loading fails, hide the icon
                StatusIcon.Visibility = Visibility.Collapsed;
            }

            // Set text and styling
            StatusTitle.Text = $"{operationType} Failed";
            StatusTitle.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red

            StatusMessage.Text = $"The {operationType.ToLower()} operation encountered an error.";

            if (!string.IsNullOrEmpty(additionalMessage))
            {
                StatusDetails.Text = additionalMessage;
                StatusDetails.Visibility = Visibility.Visible;
            }
            else
            {
                StatusDetails.Visibility = Visibility.Collapsed;
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }
    }
}
