# Windows Disk (FFU) Capture/Deployment Tool

A tool for capturing and deploying disk images using Microsoft's FFU format.

## Features

- Capture full disk images to FFU format
- Deploy FFU images to disks
- Support for both network and external drive storage
- Simple and intuitive user interface
- Automatic logging of operations

## Installation

This application is distributed as a single-file executable. No installation is required.

1. Download the latest release
2. Extract the ZIP file to a location of your choice
3. Run `FFUCaptureDeploymentTool.exe`

## Configuration

The application uses a configuration file (`config.cfg`) that is stored in the same directory as the executable. This file contains settings for:

- Network server IP address
- Image base path
- External drive settings
- Network credentials (obfuscated)
- UI settings

You can edit this file manually if needed, but it's recommended to use the application's UI to change settings.

## Usage

### Capturing an Image

1. Enter the PC model name
2. Choose whether to use network or external drive storage
   - For network storage, ensure the network is available and credentials are set
   - For external drive storage, select the drive from the External Drive Settings
3. Click the "Capture Image" button
4. Wait for the capture process to complete

### Deploying an Image

1. Enter the PC model name (must match an existing image)
2. Choose whether to use network or external drive storage
3. Click the "Deploy Image" button
4. Wait for the deployment process to complete

## Requirements

- Windows 10 or later
- Administrator privileges (required for disk operations)
- .NET runtime is included in the single-file executable

## Building from Source

To build the application as a single-file executable:

```
dotnet publish -c Release
```

The output will be in the `bin\Release\net8.0-windows\win-x64\publish` directory.
