<Window x:Class="FFUCaptureDeploymentTool.CredentialsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Enter Network Credentials" Height="280" Width="400"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="Enter network credentials for accessing image storage. For security, you must enter both username and password each time:"
                   TextWrapping="Wrap" Margin="0,0,0,15"/>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBox Grid.Column="1" x:Name="ServerTextBox" IsReadOnly="True" Margin="0,5"/>
        </Grid>

        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBox Grid.Column="1" x:Name="UsernameTextBox" Margin="0,5"/>
        </Grid>

        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <PasswordBox Grid.Column="1" x:Name="PasswordBox" Margin="0,5"/>
        </Grid>

        <TextBlock Grid.Row="4" Text="Note: Credentials will be obfuscated when saved to the configuration file. For security, previously entered credentials are never displayed in this dialog."
                   TextWrapping="Wrap" Margin="0,15,0,0" Foreground="Gray" FontStyle="Italic"/>

        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="Save" Width="80" Click="SaveButton_Click" Margin="0,0,10,0"/>
            <Button Content="Cancel" Width="80" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
