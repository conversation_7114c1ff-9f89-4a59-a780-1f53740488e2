using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Windows;
using System.Windows.Media.Imaging;
using System.Windows.Threading;

namespace FFUCaptureDeploymentTool
{
    /// <summary>
    /// Interaction logic for ProgressDialog.xaml
    /// </summary>
    public partial class ProgressDialog : Window
    {
        private readonly DispatcherTimer _updateTimer;
        private readonly Stopwatch _stopwatch;
        private CancellationTokenSource? _cancellationTokenSource;
        
        // Progress tracking
        private long _processedBytes;
        
        // Events
        public event EventHandler? CancelRequested;
        
        public ProgressDialog()
        {
            InitializeComponent();
            
            _stopwatch = new Stopwatch();
            _updateTimer = new DispatcherTimer();
            _updateTimer.Interval = TimeSpan.FromMilliseconds(500); // Update every 500ms
            _updateTimer.Tick += UpdateTimer_Tick;
        }

        public void StartOperation(string operationType, string operationDescription, CancellationTokenSource cancellationTokenSource, string? ffuFileName = null, string? ffuLocation = null, string? pcModel = null, string? storageSize = null)
        {
            _cancellationTokenSource = cancellationTokenSource;
            _processedBytes = 0;
            
            // Set operation info
            OperationTitle.Text = $"{operationType} in Progress";
            CurrentOperationText.Text = operationDescription;
            
            // Set icon based on operation type
            try
            {
                string iconName = operationType.ToLower().Contains("capture") ? "check.svg" : "warning.svg";
                var uri = new Uri($"pack://application:,,,/svg/{iconName}");
                OperationIcon.Source = new BitmapImage(uri);
            }
            catch
            {
                OperationIcon.Visibility = Visibility.Collapsed;
            }
            
            // Initialize UI
            MainProgressBar.Value = 0;
            ProgressPercentageText.Text = "0%";
            TimeRemainingText.Text = "Calculating...";

            // Set FFU information in status instead of progress messages
            if (!string.IsNullOrEmpty(ffuFileName) || !string.IsNullOrEmpty(pcModel))
            {
                var statusLines = new List<string>();

                if (!string.IsNullOrEmpty(ffuFileName))
                    statusLines.Add($"File: {ffuFileName}");

                if (!string.IsNullOrEmpty(ffuLocation))
                    statusLines.Add($"Location: {ffuLocation}");

                if (!string.IsNullOrEmpty(pcModel))
                    statusLines.Add($"Model: {pcModel}");

                if (!string.IsNullOrEmpty(storageSize))
                    statusLines.Add($"Storage: {storageSize}");

                StatusMessageText.Text = string.Join(" | ", statusLines);
            }
            else
            {
                StatusMessageText.Text = "Starting operation...";
            }
            
            // Start tracking
            _stopwatch.Start();
            _updateTimer.Start();
        }

        public void UpdateProgress(double percentage, string? statusMessage = null)
        {
            _processedBytes = (long)percentage;

            // Update UI immediately
            Dispatcher.Invoke(() =>
            {
                MainProgressBar.Value = Math.Min(percentage, 100);
                ProgressPercentageText.Text = $"{percentage:F1}%";

                // Don't overwrite FFU information with DISM status messages
                // The status now shows FFU details, not progress messages
            });
        }

        public void CompleteOperation(bool success, string? finalMessage = null)
        {
            _stopwatch.Stop();
            _updateTimer.Stop();
            
            Dispatcher.Invoke(() =>
            {
                // Update final UI state
                if (success)
                {
                    MainProgressBar.Value = 100;
                    ProgressPercentageText.Text = "100%";
                    StatusMessageText.Text = finalMessage ?? "Operation completed successfully!";
                    Title = "Operation Completed";
                }
                else
                {
                    StatusMessageText.Text = finalMessage ?? "Operation failed or was cancelled.";
                    Title = "Operation Failed";
                }
                
                // Switch buttons
                CancelButton.Visibility = Visibility.Collapsed;
                CloseButton.Visibility = Visibility.Visible;
                CloseButton.Focus();
            });
        }

        private void UpdateTimer_Tick(object? sender, EventArgs e)
        {
            // Simple ETA calculation based on percentage and elapsed time
            if (_processedBytes > 0 && _processedBytes < 100)
            {
                var elapsedSeconds = _stopwatch.Elapsed.TotalSeconds;
                var progressRate = _processedBytes / elapsedSeconds; // percentage per second
                var remainingPercentage = 100 - _processedBytes;
                var estimatedRemainingSeconds = remainingPercentage / progressRate;

                TimeRemainingText.Text = FormatTime(TimeSpan.FromSeconds(estimatedRemainingSeconds));
            }
            else
            {
                TimeRemainingText.Text = "Calculating...";
            }
        }

        private string FormatBytes(long bytes)
        {
            if (bytes >= 1024 * 1024 * 1024)
                return $"{bytes / (1024.0 * 1024.0 * 1024.0):F2} GB";
            else if (bytes >= 1024 * 1024)
                return $"{bytes / (1024.0 * 1024.0):F2} MB";
            else if (bytes >= 1024)
                return $"{bytes / 1024.0:F2} KB";
            else
                return $"{bytes} B";
        }

        private string FormatSpeed(double bytesPerSecond)
        {
            if (bytesPerSecond >= 1024 * 1024 * 1024)
                return $"{bytesPerSecond / (1024.0 * 1024.0 * 1024.0):F2} GB/s";
            else if (bytesPerSecond >= 1024 * 1024)
                return $"{bytesPerSecond / (1024.0 * 1024.0):F2} MB/s";
            else if (bytesPerSecond >= 1024)
                return $"{bytesPerSecond / 1024.0:F2} KB/s";
            else
                return $"{bytesPerSecond:F0} B/s";
        }

        private string FormatTime(TimeSpan timeSpan)
        {
            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            else
                return $"{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to cancel the operation?", 
                                       "Cancel Operation", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _cancellationTokenSource?.Cancel();
                CancelRequested?.Invoke(this, EventArgs.Empty);
                StatusMessageText.Text = "Cancelling operation...";
                CancelButton.IsEnabled = false;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            if (CancelButton.Visibility == Visibility.Visible && _cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                // Operation is still running, ask for confirmation
                var result = MessageBox.Show("An operation is still in progress. Do you want to cancel it?", 
                                           "Operation in Progress", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
                
                _cancellationTokenSource.Cancel();
                CancelRequested?.Invoke(this, EventArgs.Empty);
            }
            
            _updateTimer?.Stop();
            base.OnClosing(e);
        }
    }
}
