<Window x:Class="FFUCaptureDeploymentTool.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FFUCaptureDeploymentTool"
        mc:Ignorable="d"
        Title="Settings" Height="397" Width="287"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Settings" FontSize="20" FontWeight="SemiBold" Margin="0,0,0,15"/>

        <!-- Settings Buttons -->
        <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
            <Button Content="Network Credentials" Command="{Binding CredentialsCommand}" Margin="5,10" Height="20"/>
            <Button Content="External Drive Settings" Command="{Binding SelectUsbDriveCommand}" Margin="5,10" Height="20"/>
            <Button Content="Select Disk" Command="{Binding SelectDiskCommand}" Margin="5,10" Height="20"/>
            <Button Content="View Deployment History" Command="{Binding ViewHistoryCommand}" Margin="5,10" Height="20"/>
            <Button Content="Show Config File Location" Click="ShowConfigLocation_Click" Margin="5,10" Height="20"/>
            <Button Content="Reload Configuration" Click="ReloadConfig_Click" Margin="5,10" Height="20"/>
            <Separator Margin="0,15,0,15"/>
            <Button Content="Revoke Network Access" Click="RevokeNetworkAccess_Click" Background="#FF5B1C18" Margin="5,10" Height="26"/>
        </StackPanel>

        <!-- Close Button -->
        <Button Grid.Row="2" Content="Close" Click="CloseButton_Click" Width="100" HorizontalAlignment="Center" Margin="0,8,0,7" Height="20" Background="#FFF32121"/>
    </Grid>
</Window>
