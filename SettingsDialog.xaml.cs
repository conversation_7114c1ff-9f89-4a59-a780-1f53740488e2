using System.Windows;

namespace FFUCaptureDeploymentTool
{
    public partial class SettingsDialog : Window
    {
        public SettingsDialog()
        {
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void RevokeNetworkAccess_Click(object sender, RoutedEventArgs e)
        {
            // Get the view model from the DataContext
            if (DataContext is FFUViewModel viewModel)
            {
                // Call the RevokeNetworkAccess method
                viewModel.RevokeNetworkAccess();

                // Show a confirmation message
                MessageBox.Show(
                    "Network access has been revoked. All network connections have been closed and credentials have been cleared.",
                    "Network Access Revoked",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }

        private void ShowConfigLocation_Click(object sender, RoutedEventArgs e)
        {
            // Get the view model from the DataContext
            if (DataContext is FFUViewModel viewModel)
            {
                string configLocation = viewModel.GetConfigFileLocation();
                bool fileExists = System.IO.File.Exists(configLocation);
                
                string message = $"Config file location:\n{configLocation}\n\nFile exists: {(fileExists ? "Yes" : "No")}";
                
                if (!fileExists)
                {
                    message += "\n\nThis might explain why default values are being used.";
                }
                
                MessageBox.Show(
                    message,
                    "Configuration File Location",
                    MessageBoxButton.OK,
                    fileExists ? MessageBoxImage.Information : MessageBoxImage.Warning);
            }
        }

        private void ReloadConfig_Click(object sender, RoutedEventArgs e)
        {
            // Get the view model from the DataContext
            if (DataContext is FFUViewModel viewModel)
            {
                // Reload the configuration
                viewModel.ReloadConfig();
                
                // Show a confirmation message
                MessageBox.Show(
                    "Configuration has been reloaded. Check the log for details.",
                    "Configuration Reloaded",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
        }
    }
}
