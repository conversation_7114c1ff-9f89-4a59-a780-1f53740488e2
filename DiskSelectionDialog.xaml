<Window x:Class="FFUCaptureDeploymentTool.DiskSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FFUCaptureDeploymentTool"
        mc:Ignorable="d"
        Title="Select Physical Disk" Height="350" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Window.Resources>
        <!-- Converter for null to boolean conversion -->
        <local:NullToBooleanConverter x:Key="NullToBooleanConverter"/>

        <Style TargetType="Button">
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Select the physical disk to capture from or deploy to:"
                   FontSize="14" FontWeight="SemiBold" Margin="0,0,0,10"/>

        <!-- Disk List -->
        <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="3">
            <ListBox x:Name="DiskListBox" SelectionMode="Single" Margin="5"
                     SelectionChanged="DiskListBox_SelectionChanged">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}" Margin="5,2"/>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Border>

        <!-- Warning -->
        <TextBlock Grid.Row="2" Margin="0,10,0,10" TextWrapping="Wrap" Foreground="#F44336">
            <Run FontWeight="Bold">WARNING:</Run> Selecting the wrong disk can result in data loss.
            Make sure you select the correct disk. Disk 0 is typically the system disk.
        </TextBlock>

        <!-- Buttons -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="1" Content="OK" Click="OkButton_Click" IsEnabled="{Binding ElementName=DiskListBox, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"/>
            <Button Grid.Column="2" Content="Cancel" Click="CancelButton_Click" Background="#9E9E9E"/>
        </Grid>
    </Grid>
</Window>
