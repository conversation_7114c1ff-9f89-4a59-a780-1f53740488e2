# FFU Capture/Deployment Tool Configuration File

# Network Settings
ServerIP=***********
ImageBasePath=\\{ServerIP}\burnin\.logs\FFU_images\

# Contract Option
# Set to 'HP' to use SystemSKUnumber, or 'Lenovo' to use Model for WMIC queries
Contract=HP

# External Drive Settings
# Set to true to use external drive instead of network for image storage
UseUsbDrive=false
UsbDrivePath=
SelectedUsbDrive=

# Network Credentials (obfuscated)
# Do not edit these values directly - use the application to update credentials
NetworkUsername=
NetworkPassword=

# Disk Settings
# Set the physical disk number to capture from or deploy to (0 is typically the system disk)
SelectedDiskNumber=5

# UI Settings
# Set to false to hide all logs and only show progress bar
ShowLogging=true

# To disable logging and only show progress, change to:
# ShowLogging=false
