{"version": 2, "dgSpecHash": "LjRrV/sUAtg=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\FFUCDTool - Copy\\FFUCaptureDeploymentTool.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\costura.fody\\6.0.0\\costura.fody.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.8.2\\fody.6.8.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.illink.tasks\\9.0.8\\microsoft.net.illink.tasks.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\8.0.0\\system.codedom.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\8.0.0\\system.management.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.win-x64\\9.0.8\\microsoft.netcore.app.runtime.win-x64.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.runtime.win-x64\\9.0.8\\microsoft.windowsdesktop.app.runtime.win-x64.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.win-x64\\9.0.8\\microsoft.aspnetcore.app.runtime.win-x64.9.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.crossgen2.win-x64\\9.0.8\\microsoft.netcore.app.crossgen2.win-x64.9.0.8.nupkg.sha512"], "logs": []}